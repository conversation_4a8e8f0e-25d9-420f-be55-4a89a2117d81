{"name": "tunnel-rat", "version": "0.1.2", "description": "non gratum anus rodentum", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "scripts": {"build": "rollup -c", "postbuild": "tsc --emitDeclarationOnly", "prepublishOnly": "npm run build", "test": "jest", "ci": "yarn build && yarn test", "release": "yarn ci && yarn changeset publish"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "repository": {"type": "git", "url": "git+https://github.com/pmndrs/tunnel-rat.git"}, "keywords": ["react", "portal", "tunnel"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/pmndrs/tunnel-rat/issues"}, "devDependencies": {"@babel/core": "7.16.0", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-transform-modules-commonjs": "7.16.0", "@babel/plugin-transform-parameters": "7.16.0", "@babel/plugin-transform-runtime": "7.16.0", "@babel/plugin-transform-template-literals": "7.16.0", "@babel/preset-env": "7.16.0", "@babel/preset-react": "7.16.0", "@babel/preset-typescript": "^7.16.0", "@changesets/cli": "^2.24.3", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@types/jest": "^27.0.2", "@types/node": "^16.11.6", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.3.0", "@typescript-eslint/parser": "^5.3.0", "eslint": "^8.1.0", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jest": "^25.2.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.26.1", "eslint-plugin-react-hooks": "^4.2.0", "husky": "^7.0.4", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "lint-staged": "^11.2.6", "prettier": "^2.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^2.59.0", "rollup-plugin-size-snapshot": "^0.12.0", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^28.0.8", "typescript": "^4.4.4"}, "homepage": "https://github.com/pmndrs/tunnel-rat#readme", "dependencies": {"zustand": "^4.3.2"}}