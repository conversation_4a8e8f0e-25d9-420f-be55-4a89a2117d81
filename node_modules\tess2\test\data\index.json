[{"title": "Data copied from poly2tri c++ testbed", "source": "https://code.google.com/p/poly2tri/source/browse/#hg%2Ftestbed%2Fdata", "dateupdated": "2013-03-15", "files": [{"name": "2.dat", "content": "2", "triangles": 58}, {"name": "bird.dat", "content": "<PERSON>", "triangles": 273}, {"xxxname": "custom.dat", "throws": "invalid: polygon with crossing paths"}, {"name": "debug.dat", "content": "Butterfly-like polygon", "triangles": 198}, {"name": "diamond.dat", "content": "Diamond", "triangles": 8}, {"name": "dude.dat", "content": "Dude shape", "triangles": 92}, {"name": "dude.dat", "holes": "dude_holes.dat", "content": "<PERSON> with 2 holes", "triangles": 106}, {"name": "funny.dat", "content": "Funny polygon", "triangles": 98}, {"name": "kzer-za.dat", "content": "Kzer-<PERSON>a Dreadnought", "triangles": 206}, {"name": "nazca_heron.dat", "content": "Nazca heron", "triangles": 1034}, {"name": "nazca_monkey.dat", "content": "Nazca monkey", "triangles": 1202}, {"name": "sketchup.dat", "throws": "repeated points"}, {"name": "star.dat", "content": "Star", "triangles": 8}, {"name": "strange.dat", "triangles": 14}, {"name": "tank.dat", "content": "Tank", "triangles": 53}, {"name": "test.dat", "triangles": 4}, {"name": "debug2.dat", "content": "10000 vertices", "triangles": 9998}]}, {"title": "Data extracted from poly2tri issues", "source": "https://code.google.com/p/poly2tri/issues/list?can=1", "dateupdated": "2013-03-19", "files": [{"name": "issue39.dat", "holes": "issue39_holes.dat", "steiner": "issue39_steiner.dat", "content": "Brokepolygon with points", "triangles": 143}, {"name": "issue49.dat"}, {"name": "issue53.dat", "holes": "issue53_hole.dat", "triangles": 10}, {"name": "issue58.dat"}]}, {"title": "Data extracted from poly2tri issue #34", "source": "https://code.google.com/p/poly2tri/issues/detail?id=34", "dateupdated": "2013-03-19", "files": [{"name": "issue34/stackoverflow.dat", "throws": "RangeError: Maximum call stack size exceeded"}, {"name": "issue34/stackoverflow_uncommented.dat", "throws": "RangeError: Maximum call stack size exceeded"}, {"name": "issue34/assertion.dat", "throws": "NextFlipPoint: opposing point on constrained edge!"}, {"name": "issue34/overflow2.dat", "throws": "RangeError: Maximum call stack size exceeded"}, {"name": "issue34/dump.dat", "throws": "RangeError: Maximum call stack size exceeded"}, {"name": "issue34/dump_.dat", "triangles": 354}, {"name": "issue34/dump157.dat"}, {"name": "issue34/dump438.dat"}, {"name": "issue34/dump495.dat"}, {"name": "issue34/dump542.dat"}, {"name": "issue34/dump591.dat"}, {"name": "issue34/dump594.dat"}, {"name": "issue34/dump621.dat"}, {"name": "issue34/dump627.dat"}, {"name": "issue34/dump640.dat"}, {"name": "issue34/dump675.dat"}, {"name": "issue34/dump690.dat"}, {"name": "issue34/dump720.dat"}, {"name": "issue34/dump737.dat"}, {"name": "issue34/dump805.dat", "content": "Long polygon", "triangles": 446}, {"name": "issue34/dump857.dat"}, {"name": "issue34/dump862.dat"}, {"name": "issue34/dump910.dat"}, {"name": "issue34/dump1208.dat", "content": "Big round polygon", "triangles": 380}]}, {"title": "Data extracted from poly2tri forum", "source": "https://groups.google.com/forum/?fromgroups=#!topic/poly2tri/H1OzouuOZ_s", "dateupdated": "2013-04-08", "files": [{"name": "map_invalid.dat", "throws": "weak polygon with touching edges"}, {"name": "map_fixed.dat", "content": "Map indoor", "triangles": 121}]}, {"title": "France borders", "source": "http://commons.wikimedia.org/wiki/File:Blank_France_map,_no_Departments.svg", "dateupdated": "2013-04-19", "files": [{"name": "France.dat", "content": "France", "triangles": 768}]}, {"title": "Berkeley CS 274 tests", "source": "http://www.cs.berkeley.edu/~jrs/274s03/proj.html", "dateupdated": "2013-04-19", "files": [{"name": "spiral.dat", "content": "<PERSON><PERSON><PERSON>", "triangles": 13}]}, {"title": "GLU examples", "dateupdated": "2013-09-02", "files": [{"name": "glu_example.dat", "content": "GLU example"}, {"name": "glu_winding.dat", "content": "GLU winding"}]}]