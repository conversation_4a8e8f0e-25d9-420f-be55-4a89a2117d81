/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.2.3 public/models/64f1a714fe61576b46f27ca2.glb -o src/components/Avatar.jsx -k -r public
*/

import { useAnimations, useGLTF } from "@react-three/drei";
import { useFrame } from "@react-three/fiber";
import { button, useControls } from "leva";
import React, { useEffect, useRef, useState } from "react";

import * as THREE from "three";
import { useChat } from "../hooks/useChat";

const facialExpressions = {
  default: {},
  smile: {
    browInnerUp: 0.17,
    eyeSquintLeft: 0.4,
    eyeSquintRight: 0.44,
    noseSneerLeft: 0.1700000727403593,
    noseSneerRight: 0.14000002836874015,
    mouthPressLeft: 0.61,
    mouthPressRight: 0.41000000000000003,
  },
  funnyFace: {
    jawLeft: 0.63,
    mouthPucker: 0.53,
    noseSneerLeft: 1,
    noseSneerRight: 0.39,
    mouthLeft: 1,
    eyeLookUpLeft: 1,
    eyeLookUpRight: 1,
    cheekPuff: 0.9999924982764238,
    mouthDimpleLeft: 0.414743888682652,
    mouthRollLower: 0.32,
    mouthSmileLeft: 0.35499733688813034,
    mouthSmileRight: 0.35499733688813034,
  },
  sad: {
    mouthFrownLeft: 1,
    mouthFrownRight: 1,
    mouthShrugLower: 0.78341,
    browInnerUp: 0.452,
    eyeSquintLeft: 0.72,
    eyeSquintRight: 0.75,
    eyeLookDownLeft: 0.5,
    eyeLookDownRight: 0.5,
    jawForward: 1,
  },
  surprised: {
    eyeWideLeft: 0.5,
    eyeWideRight: 0.5,
    jawOpen: 0.351,
    mouthFunnel: 1,
    browInnerUp: 1,
  },
  angry: {
    browDownLeft: 1,
    browDownRight: 1,
    eyeSquintLeft: 1,
    eyeSquintRight: 1,
    jawForward: 1,
    jawLeft: 1,
    mouthShrugLower: 1,
    noseSneerLeft: 1,
    noseSneerRight: 0.42,
    eyeLookDownLeft: 0.16,
    eyeLookDownRight: 0.16,
    cheekSquintLeft: 1,
    cheekSquintRight: 1,
    mouthClose: 0.23,
    mouthFunnel: 0.63,
    mouthDimpleRight: 1,
  },
  crazy: {
    browInnerUp: 0.9,
    jawForward: 1,
    noseSneerLeft: 0.5700000000000001,
    noseSneerRight: 0.51,
    eyeLookDownLeft: 0.39435766259644545,
    eyeLookUpRight: 0.4039761421719682,
    eyeLookInLeft: 0.9618479575523053,
    eyeLookInRight: 0.9618479575523053,
    jawOpen: 0.9618479575523053,
    mouthDimpleLeft: 0.9618479575523053,
    mouthDimpleRight: 0.9618479575523053,
    mouthStretchLeft: 0.27893590769016857,
    mouthStretchRight: 0.2885543872656917,
    mouthSmileLeft: 0.5578718153803371,
    mouthSmileRight: 0.38473918302092225,
    tongueOut: 0.9618479575523053,
  },
};

const corresponding = {
  A: "viseme_PP",
  B: "viseme_kk",
  C: "viseme_I",
  D: "viseme_AA",
  E: "viseme_O",
  F: "viseme_U",
  G: "viseme_FF",
  H: "viseme_TH",
  X: "viseme_PP",
};

let setupMode = false;

export function Avatar(props) {
  const { nodes, materials, scene } = useGLTF(
    "/models/64f1a714fe61576b46f27ca2.glb"
  );

  const { message, onMessagePlayed, chat } = useChat();

  const [lipsync, setLipsync] = useState();
  const [voices, setVoices] = useState([]);
  const [selectedVoice, setSelectedVoice] = useState(null);

  // Initialize speech synthesis voices
  useEffect(() => {
    const initVoices = () => {
      console.log("Initializing speech synthesis voices");
      const availableVoices = window.speechSynthesis?.getVoices() || [];
      console.log("Available voices:", availableVoices.map(v => `${v.name} (${v.lang})`));
      setVoices(availableVoices);

      // Find the best voice for our needs
      findBestVoice(availableVoices);
    };

    const findBestVoice = (availableVoices) => {
      // Priority order for voice selection (most realistic female voices):
      // 1. Microsoft Jessa (newer neural voice - very realistic)
      // 2. Microsoft Catherine (newer neural voice - very realistic)
      // 3. Google US English female voice (very natural)
      // 4. Microsoft Aria (newer, more natural voice)
      // 5. Microsoft Jenny (newer, more natural voice)
      // 6. Any voice with "neural" in the name (newer AI voices)
      // 7. Any voice with "natural" in the name
      // 8. Any English female voice
      // 9. Default voice

      console.log("Finding best voice from", availableVoices.length, "available voices");

      // Log all voices for debugging
      availableVoices.forEach(v => {
        console.log(`Voice: ${v.name}, Lang: ${v.lang}, Default: ${v.default}, Local: ${v.localService}`);
      });

      // Check for Microsoft Jessa (newer neural voice - very realistic)
      let voice = availableVoices.find(v => v.name.includes('Jessa'));

      // Check for Microsoft Catherine (newer neural voice - very realistic)
      if (!voice) {
        voice = availableVoices.find(v => v.name.includes('Catherine'));
      }

      // Check for Google US English female voice (very natural)
      if (!voice) {
        voice = availableVoices.find(v =>
          (v.name.includes('Google') || v.name.includes('Chrome')) &&
          v.lang.includes('en-US') &&
          (v.name.includes('Female') || v.name.includes('female'))
        );
      }

      // Check for Microsoft Aria (newer, more natural voice)
      if (!voice) {
        voice = availableVoices.find(v => v.name.includes('Aria'));
      }

      // Check for Microsoft Jenny (newer, more natural voice)
      if (!voice) {
        voice = availableVoices.find(v => v.name.includes('Jenny'));
      }

      // Check for any voice with "neural" in the name (newer AI voices)
      if (!voice) {
        voice = availableVoices.find(v =>
          v.name.toLowerCase().includes('neural') &&
          (v.lang.includes('en-') || v.lang.includes('en_'))
        );
      }

      // Check for any voice with "natural" in the name
      if (!voice) {
        voice = availableVoices.find(v =>
          v.name.toLowerCase().includes('natural') &&
          (v.lang.includes('en-') || v.lang.includes('en_'))
        );
      }

      // Check for Microsoft Zira (English US female voice)
      if (!voice) {
        voice = availableVoices.find(v => v.name.includes('Zira'));
      }

      // Check for any English female voice
      if (!voice) {
        voice = availableVoices.find(v =>
          (v.lang.includes('en-') || v.lang.includes('en_')) &&
          (v.name.toLowerCase().includes('female') || v.name.toLowerCase().includes('girl') ||
           v.name.toLowerCase().includes('woman') || !v.name.toLowerCase().includes('male'))
        );
      }

      // Check for any English voice as fallback
      if (!voice) {
        voice = availableVoices.find(v => v.lang.includes('en-') || v.lang.includes('en_'));
      }

      // If we found a voice, set it
      if (voice) {
        console.log(`Selected voice: ${voice.name} (${voice.lang})`);
        setSelectedVoice(voice);
      } else {
        console.log("No suitable voice found, using default");
      }
    };

    if (window.speechSynthesis) {
      // Chrome loads voices asynchronously
      if (window.speechSynthesis.onvoiceschanged !== undefined) {
        window.speechSynthesis.onvoiceschanged = initVoices;
      }

      // Try to get voices immediately (works in Firefox)
      initVoices();
    }
  }, []);

  useEffect(() => {
    console.log("Current message:", message);
    console.log("Message text:", message?.text);
    console.log("Message animation:", message?.animation);
    console.log("Message facial expression:", message?.facialExpression);
    console.log("Message audio:", message?.audio ? "Present" : "Missing");
    console.log("Message lipsync:", message?.lipsync ? "Present" : "Missing");

    if (!message) {
      setAnimation("Idle");
      return;
    }

    // Log the animation being set
    console.log("Setting animation to:", message.animation || "Talking_0");
    setAnimation(message.animation || "Talking_0");

    // Log the facial expression being set
    console.log("Setting facial expression to:", message.facialExpression || "smile");
    setFacialExpression(message.facialExpression || "smile");

    setLipsync(message.lipsync || null);

    // Use Web Speech API with Windows voices
    if (message.text && window.speechSynthesis) {
      try {
        // Cancel any ongoing speech
        window.speechSynthesis.cancel();

        // Prepare the text for more natural speech
        let speechText = message.text;

        // Advanced text preprocessing for more natural speech

        // 1. Add SSML-like breathing and pauses (browsers will ignore SSML tags but keep the spaces)
        speechText = speechText
          // Add slight pauses after sentences
          .replace(/\./g, ". ")
          .replace(/\!/g, "! ")
          .replace(/\?/g, "? ")
          // Add slight pauses after commas
          .replace(/\,/g, ", ")
          // Add slight pauses for natural breathing
          .replace(/(\w{10,})/g, "$1 ");

        // 2. Add subtle emphasis to emotional words
        const emotionalWords = [
          'love', 'miss', 'happy', 'sad', 'feel', 'think', 'want', 'need',
          'sorry', 'please', 'thank', 'sweet', 'cute', 'nice', 'good', 'bad',
          'wonderful', 'beautiful', 'amazing', 'awesome', 'great', 'perfect'
        ];

        // Create a regex pattern for all emotional words
        const emotionalWordsPattern = new RegExp(`\\b(${emotionalWords.join('|')})\\b`, 'gi');

        // Add spaces around emotional words for emphasis
        speechText = speechText
          .replace(emotionalWordsPattern, " $1 ")
          .replace(/\s+/g, " ") // Normalize spaces
          .trim();

        // 3. Add subtle variations in pitch and rate throughout the text
        // We'll use special characters that won't be pronounced but will affect timing

        // Add a slight pause at the beginning
        speechText = " ... " + speechText;

        // Create a new speech utterance with the enhanced text
        const utterance = new SpeechSynthesisUtterance(speechText);

        // Log all available voices
        console.log("All available voices:", voices.map(v => `${v.name} (${v.lang})`));

        // Always use the selected English voice
        if (selectedVoice) {
          console.log("Using selected voice:", selectedVoice.name);
          utterance.voice = selectedVoice;
          utterance.lang = 'en-US'; // Ensure English language is set
        } else {
          console.log("No selected voice found, using default");
        }

        // Fine-tune speech parameters for a more realistic, natural female voice
        utterance.rate = 0.92;     // Slightly slower for more clarity and naturalness
        utterance.pitch = 1.12;    // Slightly higher pitch for a feminine voice, but not too high
        utterance.volume = 1.0;    // Full volume

        // Use a slightly randomized pitch and rate for each utterance
        // This makes each response sound slightly different and more natural
        const pitchVariation = Math.random() * 0.06 - 0.03; // Small random variation (-0.03 to +0.03)
        const rateVariation = Math.random() * 0.04 - 0.02;  // Small random variation (-0.02 to +0.02)

        utterance.pitch = 1.12 + pitchVariation; // Base pitch with small random variation
        utterance.rate = 0.92 + rateVariation;   // Base rate with small random variation

        console.log(`Using pitch: ${utterance.pitch}, rate: ${utterance.rate}`);

        // Set event handlers
        utterance.onend = () => {
          console.log("Speech ended");
          onMessagePlayed();
        };

        utterance.onerror = (error) => {
          console.error("Speech error:", error);
          // If speech fails, use a timer based on text length
          const words = message.text.split(' ').length;
          const duration = Math.max(2, words / 2.5) * 1000;
          setTimeout(onMessagePlayed, duration);
        };

        // Speak the text
        window.speechSynthesis.speak(utterance);
        console.log("Speaking:", message.text);

        // Also set a backup timer in case the speech events don't fire
        const words = message.text.split(' ').length;
        const backupDuration = Math.max(3, words / 1.5) * 1000; // Longer duration for backup
        console.log(`Setting backup timer for ${backupDuration}ms`);
        const backupTimer = setTimeout(() => {
          console.log("Backup timer triggered");
          onMessagePlayed();
        }, backupDuration);

        // Clear the backup timer if speech ends normally
        utterance.onend = () => {
          console.log("Speech ended, clearing backup timer");
          clearTimeout(backupTimer);
          onMessagePlayed();
        };
      } catch (error) {
        console.error("Error with speech synthesis:", error);
        // If speech synthesis fails, use a timer based on text length
        const words = message.text.split(' ').length;
        const duration = Math.max(2, words / 2.5) * 1000;
        setTimeout(onMessagePlayed, duration);
      }
    } else {
      // If Web Speech API is not available, use a timer based on text length
      const words = message.text.split(' ').length;
      const duration = Math.max(2, words / 2.5) * 1000;
      console.log(`No speech synthesis available. Message has ${words} words, setting duration to ${duration}ms`);
      setTimeout(onMessagePlayed, duration);
    }
  }, [message]);

  const { animations } = useGLTF("/models/animations.glb");

  const group = useRef();
  const { actions, mixer } = useAnimations(animations, group);
  const [animation, setAnimation] = useState(
    animations.find((a) => a.name === "Idle") ? "Idle" : animations[0].name // Check if Idle animation exists otherwise use first animation
  );
  useEffect(() => {
    console.log("Available animations:", Object.keys(actions));
    console.log("Current animation:", animation);

    if (!actions[animation]) {
      console.error(`Animation "${animation}" not found!`);
      // Try to use a default animation if the requested one doesn't exist
      const availableAnimations = Object.keys(actions);
      if (availableAnimations.length > 0) {
        console.log(`Falling back to "${availableAnimations[0]}" animation`);
        setAnimation(availableAnimations[0]);
        return;
      }
    }

    try {
      actions[animation]
        .reset()
        .fadeIn(mixer.stats.actions.inUse === 0 ? 0 : 0.5)
        .play();
      console.log(`Successfully playing animation: ${animation}`);
    } catch (error) {
      console.error(`Error playing animation ${animation}:`, error);
      // Try to play a default animation if there's an error
      try {
        const defaultAnimation = "Idle";
        console.log(`Trying to play default animation: ${defaultAnimation}`);
        actions[defaultAnimation]
          .reset()
          .fadeIn(0.5)
          .play();
      } catch (fallbackError) {
        console.error("Error playing fallback animation:", fallbackError);
      }
    }

    return () => {
      try {
        actions[animation].fadeOut(0.5);
      } catch (error) {
        console.error(`Error fading out animation ${animation}:`, error);
      }
    };
  }, [animation]);

  const lerpMorphTarget = (target, value, speed = 0.1) => {
    scene.traverse((child) => {
      if (child.isSkinnedMesh && child.morphTargetDictionary) {
        const index = child.morphTargetDictionary[target];
        if (
          index === undefined ||
          child.morphTargetInfluences[index] === undefined
        ) {
          return;
        }
        child.morphTargetInfluences[index] = THREE.MathUtils.lerp(
          child.morphTargetInfluences[index],
          value,
          speed
        );

        if (!setupMode) {
          try {
            set({
              [target]: value,
            });
          } catch (e) {}
        }
      }
    });
  };

  const [blink, setBlink] = useState(false);
  const [winkLeft, setWinkLeft] = useState(false);
  const [winkRight, setWinkRight] = useState(false);
  const [facialExpression, setFacialExpression] = useState("");
  const [audio, setAudio] = useState();

  useFrame(() => {
    !setupMode &&
      Object.keys(nodes.EyeLeft.morphTargetDictionary).forEach((key) => {
        const mapping = facialExpressions[facialExpression];
        if (key === "eyeBlinkLeft" || key === "eyeBlinkRight") {
          return; // eyes wink/blink are handled separately
        }
        if (mapping && mapping[key]) {
          lerpMorphTarget(key, mapping[key], 0.1);
        } else {
          lerpMorphTarget(key, 0, 0.1);
        }
      });

    lerpMorphTarget("eyeBlinkLeft", blink || winkLeft ? 1 : 0, 0.5);
    lerpMorphTarget("eyeBlinkRight", blink || winkRight ? 1 : 0, 0.5);

    // LIPSYNC
    if (setupMode) {
      return;
    }

    const appliedMorphTargets = [];
    if (message && lipsync && lipsync.mouthCues && audio) {
      try {
        const currentAudioTime = audio.currentTime;

        // Add a slight delay to the lip movements to make them appear slower
        // This simulates the natural delay between hearing and lip movement
        const lipSyncDelay = 0.1; // 100ms delay
        const adjustedTime = Math.max(0, currentAudioTime - lipSyncDelay);

        for (let i = 0; i < lipsync.mouthCues.length; i++) {
          const mouthCue = lipsync.mouthCues[i];
          if (
            adjustedTime >= mouthCue.start &&
            adjustedTime <= mouthCue.end &&
            mouthCue.value &&
            corresponding[mouthCue.value]
          ) {
            appliedMorphTargets.push(corresponding[mouthCue.value]);
            // Use a slower lerp speed for smoother transitions
            lerpMorphTarget(corresponding[mouthCue.value], 1, 0.1);
            break;
          }
        }
      } catch (error) {
        console.error("Error in lipsync processing:", error);
      }
    } else if (message) {
      // If no lipsync data or audio, still animate the mouth randomly for a talking effect
      // Use a slower random animation by only changing the mouth every few frames
      if (Math.random() > 0.8) { // Lower probability = less frequent changes = slower animation
        const randomPhoneme = Object.values(corresponding)[Math.floor(Math.random() * Object.values(corresponding).length)];
        appliedMorphTargets.push(randomPhoneme);
        // Use a slower lerp speed for smoother transitions
        lerpMorphTarget(randomPhoneme, 1, 0.1);
      }
    }

    Object.values(corresponding).forEach((value) => {
      if (appliedMorphTargets.includes(value)) {
        return;
      }
      lerpMorphTarget(value, 0, 0.1);
    });
  });

  useControls("FacialExpressions", {
    chat: button(() => chat()),
    winkLeft: button(() => {
      setWinkLeft(true);
      setTimeout(() => setWinkLeft(false), 300);
    }),
    winkRight: button(() => {
      setWinkRight(true);
      setTimeout(() => setWinkRight(false), 300);
    }),
    animation: {
      value: animation,
      options: animations.map((a) => a.name),
      onChange: (value) => setAnimation(value),
    },
    facialExpression: {
      options: Object.keys(facialExpressions),
      onChange: (value) => setFacialExpression(value),
    },
    enableSetupMode: button(() => {
      setupMode = true;
    }),
    disableSetupMode: button(() => {
      setupMode = false;
    }),
    logMorphTargetValues: button(() => {
      const emotionValues = {};
      Object.keys(nodes.EyeLeft.morphTargetDictionary).forEach((key) => {
        if (key === "eyeBlinkLeft" || key === "eyeBlinkRight") {
          return; // eyes wink/blink are handled separately
        }
        const value =
          nodes.EyeLeft.morphTargetInfluences[
            nodes.EyeLeft.morphTargetDictionary[key]
          ];
        if (value > 0.01) {
          emotionValues[key] = value;
        }
      });
      console.log(JSON.stringify(emotionValues, null, 2));
    }),
  });

  const [, set] = useControls("MorphTarget", () =>
    Object.assign(
      {},
      ...Object.keys(nodes.EyeLeft.morphTargetDictionary).map((key) => {
        return {
          [key]: {
            label: key,
            value: 0,
            min: nodes.EyeLeft.morphTargetInfluences[
              nodes.EyeLeft.morphTargetDictionary[key]
            ],
            max: 1,
            onChange: (val) => {
              if (setupMode) {
                lerpMorphTarget(key, val, 1);
              }
            },
          },
        };
      })
    )
  );

  useEffect(() => {
    let blinkTimeout;
    const nextBlink = () => {
      blinkTimeout = setTimeout(() => {
        setBlink(true);
        setTimeout(() => {
          setBlink(false);
          nextBlink();
        }, 200);
      }, THREE.MathUtils.randInt(1000, 5000));
    };
    nextBlink();
    return () => clearTimeout(blinkTimeout);
  }, []);

  return (
    <group {...props} dispose={null} ref={group}>
      <primitive object={nodes.Hips} />
      <skinnedMesh
        name="Wolf3D_Body"
        geometry={nodes.Wolf3D_Body.geometry}
        material={materials.Wolf3D_Body}
        skeleton={nodes.Wolf3D_Body.skeleton}
      />
      <skinnedMesh
        name="Wolf3D_Outfit_Bottom"
        geometry={nodes.Wolf3D_Outfit_Bottom.geometry}
        material={materials.Wolf3D_Outfit_Bottom}
        skeleton={nodes.Wolf3D_Outfit_Bottom.skeleton}
      />
      <skinnedMesh
        name="Wolf3D_Outfit_Footwear"
        geometry={nodes.Wolf3D_Outfit_Footwear.geometry}
        material={materials.Wolf3D_Outfit_Footwear}
        skeleton={nodes.Wolf3D_Outfit_Footwear.skeleton}
      />
      <skinnedMesh
        name="Wolf3D_Outfit_Top"
        geometry={nodes.Wolf3D_Outfit_Top.geometry}
        material={materials.Wolf3D_Outfit_Top}
        skeleton={nodes.Wolf3D_Outfit_Top.skeleton}
      />
      <skinnedMesh
        name="Wolf3D_Hair"
        geometry={nodes.Wolf3D_Hair.geometry}
        material={materials.Wolf3D_Hair}
        skeleton={nodes.Wolf3D_Hair.skeleton}
      />
      <skinnedMesh
        name="EyeLeft"
        geometry={nodes.EyeLeft.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeLeft.skeleton}
        morphTargetDictionary={nodes.EyeLeft.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeLeft.morphTargetInfluences}
      />
      <skinnedMesh
        name="EyeRight"
        geometry={nodes.EyeRight.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeRight.skeleton}
        morphTargetDictionary={nodes.EyeRight.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeRight.morphTargetInfluences}
      />
      <skinnedMesh
        name="Wolf3D_Head"
        geometry={nodes.Wolf3D_Head.geometry}
        material={materials.Wolf3D_Skin}
        skeleton={nodes.Wolf3D_Head.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Head.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Head.morphTargetInfluences}
      />
      <skinnedMesh
        name="Wolf3D_Teeth"
        geometry={nodes.Wolf3D_Teeth.geometry}
        material={materials.Wolf3D_Teeth}
        skeleton={nodes.Wolf3D_Teeth.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Teeth.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Teeth.morphTargetInfluences}
      />
    </group>
  );
}

useGLTF.preload("/models/64f1a714fe61576b46f27ca2.glb");
useGLTF.preload("/models/animations.glb");
