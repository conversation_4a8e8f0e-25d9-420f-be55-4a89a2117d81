// This file is part of meshoptimizer library and is distributed under the terms of MIT License.
// Copyright (C) 2016-2022, by <PERSON><PERSON><PERSON> (<EMAIL>)
var MeshoptSimplifier = (function() {
	"use strict";

	// Built with clang version 14.0.4 (https://github.com/llvm/llvm-project 29f1039a7285a5c3a9c353d054140bf2556d4c4d)
	// Built from meshoptimizer 0.18
	var wasm = "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";

	// Used to unpack wasm
	var wasmpack = new Uint8Array([32,0,65,2,1,106,34,33,3,128,11,4,13,64,6,253,10,7,15,116,127,5,8,12,40,16,19,54,20,9,27,255,113,17,42,67,24,23,146,148,18,14,22,45,70,69,56,114,101,21,25,63,75,136,108,28,118,29,73,115]);

	if (typeof WebAssembly !== 'object') {
		// This module requires WebAssembly to function
		return {
			supported: false,
		};
	}

	var instance;

	var promise =
		WebAssembly.instantiate(unpack(wasm), {})
		.then(function(result) {
			instance = result.instance;
			instance.exports.__wasm_call_ctors();
		});

	function unpack(data) {
		var result = new Uint8Array(data.length);
		for (var i = 0; i < data.length; ++i) {
			var ch = data.charCodeAt(i);
			result[i] = ch > 96 ? ch - 71 : ch > 64 ? ch - 65 : ch > 47 ? ch + 4 : ch > 46 ? 63 : 62;
		}
		var write = 0;
		for (var i = 0; i < data.length; ++i) {
			result[write++] = (result[i] < 60) ? wasmpack[result[i]] : (result[i] - 60) * 64 + result[++i];
		}
		return result.buffer.slice(0, write);
	}

	function assert(cond) {
		if (!cond) {
			throw new Error("Assertion failed");
		}
	}

	function bytes(view) {
		return new Uint8Array(view.buffer, view.byteOffset, view.byteLength);
	}

	function reorder(indices, vertices) {
		var sbrk = instance.exports.sbrk;
		var ip = sbrk(indices.length * 4);
		var rp = sbrk(vertices * 4);
		var heap = new Uint8Array(instance.exports.memory.buffer);
		var indices8 = bytes(indices);
		heap.set(indices8, ip);
		var unique = instance.exports.meshopt_optimizeVertexFetchRemap(rp, ip, indices.length, vertices);
		// heap may have grown
		heap = new Uint8Array(instance.exports.memory.buffer);
		var remap = new Uint32Array(vertices);
		new Uint8Array(remap.buffer).set(heap.subarray(rp, rp + vertices * 4));
		indices8.set(heap.subarray(ip, ip + indices.length * 4));
		sbrk(ip - sbrk(0));

		for (var i = 0; i < indices.length; ++i)
			indices[i] = remap[indices[i]];

		return [remap, unique];
	}

	function maxindex(source) {
		var result = 0;
		for (var i = 0; i < source.length; ++i) {
			var index = source[i];
			result = result < index ? index : result;
		}
		return result;
	}

	function simplify(fun, indices, index_count, vertex_positions, vertex_count, vertex_positions_stride, target_index_count, target_error, options) {
		var sbrk = instance.exports.sbrk;
		var te = sbrk(4);
		var ti = sbrk(index_count * 4);
		var sp = sbrk(vertex_count * vertex_positions_stride);
		var si = sbrk(index_count * 4);
		var heap = new Uint8Array(instance.exports.memory.buffer);
		heap.set(bytes(vertex_positions), sp);
		heap.set(bytes(indices), si);
		var result = fun(ti, si, index_count, sp, vertex_count, vertex_positions_stride, target_index_count, target_error, options, te);
		// heap may have grown
		heap = new Uint8Array(instance.exports.memory.buffer);
		var target = new Uint32Array(result);
		bytes(target).set(heap.subarray(ti, ti + result * 4));
		var error = new Float32Array(1);
		bytes(error).set(heap.subarray(te, te + 4));
		sbrk(te - sbrk(0));
		return [target, error[0]];
	}

	function simplifyScale(fun, vertex_positions, vertex_count, vertex_positions_stride) {
		var sbrk = instance.exports.sbrk;
		var sp = sbrk(vertex_count * vertex_positions_stride);
		var heap = new Uint8Array(instance.exports.memory.buffer);
		heap.set(bytes(vertex_positions), sp);
		var result = fun(sp, vertex_count, vertex_positions_stride);
		sbrk(sp - sbrk(0));
		return result;
	}

	var simplifyOptions = {
		LockBorder: 1,
	};

	return {
		ready: promise,
		supported: true,

		compactMesh: function(indices) {
			assert(indices instanceof Uint32Array || indices instanceof Int32Array || indices instanceof Uint16Array || indices instanceof Int16Array);
			assert(indices.length % 3 == 0);

			var indices32 = indices.BYTES_PER_ELEMENT == 4 ? indices : new Uint32Array(indices);
			return reorder(indices32, maxindex(indices) + 1);
		},

		simplify: function(indices, vertex_positions, vertex_positions_stride, target_index_count, target_error, flags) {
			assert(indices instanceof Uint32Array || indices instanceof Int32Array || indices instanceof Uint16Array || indices instanceof Int16Array);
			assert(indices.length % 3 == 0);
			assert(vertex_positions instanceof Float32Array);
			assert(vertex_positions.length % vertex_positions_stride == 0);
			assert(vertex_positions_stride >= 3);
			assert(target_index_count % 3 == 0);

			var options = 0;
			for (var i = 0; i < (flags ? flags.length : 0); ++i) {
				options |= simplifyOptions[flags[i]];
			}

			var indices32 = indices.BYTES_PER_ELEMENT == 4 ? indices : new Uint32Array(indices);
			var result = simplify(instance.exports.meshopt_simplify, indices32, indices.length, vertex_positions, vertex_positions.length, vertex_positions_stride * 4, target_index_count, target_error, options);
			result[0] = (indices instanceof Uint32Array) ? result[0] : new indices.constructor(result[0]);

			return result;
		},

		getScale: function(vertex_positions, vertex_positions_stride) {
			assert(vertex_positions instanceof Float32Array);
			assert(vertex_positions.length % vertex_positions_stride == 0);

			return simplifyScale(instance.exports.meshopt_simplifyScale, vertex_positions, vertex_positions.length, vertex_positions_stride * 4);
		},
	};
})();

export { MeshoptSimplifier };
