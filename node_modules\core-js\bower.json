{"name": "core.js", "main": "client/core.js", "version": "1.2.7", "description": "Standard Library", "keywords": ["ES6", "ECMAScript 6", "ES7", "ECMAScript 7", "Map", "Set", "WeakMap", "WeakSet", "Dict", "Promise", "Symbol", "console"], "authors": ["<PERSON> <<EMAIL>> (http://zloirock.ru/)"], "license": "MIT", "homepage": "https://github.com/zloirock/core-js", "repository": {"type": "git", "url": "https://github.com/zloirock/core-js.git"}, "ignore": ["build", "node_modules", "tests"]}