{"version": 3, "sources": ["../../@babel/runtime/helpers/esm/extends.js", "../../zustand/esm/index.js"], "sourcesContent": ["export default function _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}", "import { useReducer, useRef, useDebugValue, useEffect, useLayoutEffect } from 'react';\n\nfunction createStore(createState) {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (nextState !== state) {\n      const previousState = state;\n      state = replace ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const subscribeWithSelector = (listener, selector = getState, equalityFn = Object.is) => {\n    console.warn(\"[DEPRECATED] Please use `subscribeWithSelector` middleware\");\n    let currentSlice = selector(state);\n    function listenerToAdd() {\n      const nextSlice = selector(state);\n      if (!equalityFn(currentSlice, nextSlice)) {\n        const previousSlice = currentSlice;\n        listener(currentSlice = nextSlice, previousSlice);\n      }\n    }\n    listeners.add(listenerToAdd);\n    return () => listeners.delete(listenerToAdd);\n  };\n  const subscribe = (listener, selector, equalityFn) => {\n    if (selector || equalityFn) {\n      return subscribeWithSelector(listener, selector, equalityFn);\n    }\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => listeners.clear();\n  const api = { setState, getState, subscribe, destroy };\n  state = createState(setState, getState, api);\n  return api;\n}\n\nconst isSSR = typeof window === \"undefined\" || !window.navigator || /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent);\nconst useIsomorphicLayoutEffect = isSSR ? useEffect : useLayoutEffect;\nfunction create(createState) {\n  const api = typeof createState === \"function\" ? createStore(createState) : createState;\n  const useStore = (selector = api.getState, equalityFn = Object.is) => {\n    const [, forceUpdate] = useReducer((c) => c + 1, 0);\n    const state = api.getState();\n    const stateRef = useRef(state);\n    const selectorRef = useRef(selector);\n    const equalityFnRef = useRef(equalityFn);\n    const erroredRef = useRef(false);\n    const currentSliceRef = useRef();\n    if (currentSliceRef.current === void 0) {\n      currentSliceRef.current = selector(state);\n    }\n    let newStateSlice;\n    let hasNewStateSlice = false;\n    if (stateRef.current !== state || selectorRef.current !== selector || equalityFnRef.current !== equalityFn || erroredRef.current) {\n      newStateSlice = selector(state);\n      hasNewStateSlice = !equalityFn(currentSliceRef.current, newStateSlice);\n    }\n    useIsomorphicLayoutEffect(() => {\n      if (hasNewStateSlice) {\n        currentSliceRef.current = newStateSlice;\n      }\n      stateRef.current = state;\n      selectorRef.current = selector;\n      equalityFnRef.current = equalityFn;\n      erroredRef.current = false;\n    });\n    const stateBeforeSubscriptionRef = useRef(state);\n    useIsomorphicLayoutEffect(() => {\n      const listener = () => {\n        try {\n          const nextState = api.getState();\n          const nextStateSlice = selectorRef.current(nextState);\n          if (!equalityFnRef.current(currentSliceRef.current, nextStateSlice)) {\n            stateRef.current = nextState;\n            currentSliceRef.current = nextStateSlice;\n            forceUpdate();\n          }\n        } catch (error) {\n          erroredRef.current = true;\n          forceUpdate();\n        }\n      };\n      const unsubscribe = api.subscribe(listener);\n      if (api.getState() !== stateBeforeSubscriptionRef.current) {\n        listener();\n      }\n      return unsubscribe;\n    }, []);\n    const sliceToReturn = hasNewStateSlice ? newStateSlice : currentSliceRef.current;\n    useDebugValue(sliceToReturn);\n    return sliceToReturn;\n  };\n  Object.assign(useStore, api);\n  useStore[Symbol.iterator] = function() {\n    console.warn(\"[useStore, api] = create() is deprecated and will be removed in v4\");\n    const items = [useStore, api];\n    return {\n      next() {\n        const done = items.length <= 0;\n        return { value: items.shift(), done };\n      }\n    };\n  };\n  return useStore;\n}\n\nexport { create as default };\n"], "mappings": ";;;;;;;;AAAe,SAAR,WAA4B;AACjC,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;;;ACbA,mBAA8E;AAE9E,SAAS,YAAY,aAAa;AAChC,MAAI;AACJ,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,WAAW,CAAC,SAAS,YAAY;AACrC,UAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACnE,QAAI,cAAc,OAAO;AACvB,YAAM,gBAAgB;AACtB,cAAQ,UAAU,YAAY,OAAO,OAAO,CAAC,GAAG,OAAO,SAAS;AAChE,gBAAU,QAAQ,CAAC,aAAa,SAAS,OAAO,aAAa,CAAC;AAAA,IAChE;AAAA,EACF;AACA,QAAM,WAAW,MAAM;AACvB,QAAM,wBAAwB,CAAC,UAAU,WAAW,UAAU,aAAa,OAAO,OAAO;AACvF,YAAQ,KAAK,4DAA4D;AACzE,QAAI,eAAe,SAAS,KAAK;AACjC,aAAS,gBAAgB;AACvB,YAAM,YAAY,SAAS,KAAK;AAChC,UAAI,CAAC,WAAW,cAAc,SAAS,GAAG;AACxC,cAAM,gBAAgB;AACtB,iBAAS,eAAe,WAAW,aAAa;AAAA,MAClD;AAAA,IACF;AACA,cAAU,IAAI,aAAa;AAC3B,WAAO,MAAM,UAAU,OAAO,aAAa;AAAA,EAC7C;AACA,QAAM,YAAY,CAAC,UAAU,UAAU,eAAe;AACpD,QAAI,YAAY,YAAY;AAC1B,aAAO,sBAAsB,UAAU,UAAU,UAAU;AAAA,IAC7D;AACA,cAAU,IAAI,QAAQ;AACtB,WAAO,MAAM,UAAU,OAAO,QAAQ;AAAA,EACxC;AACA,QAAM,UAAU,MAAM,UAAU,MAAM;AACtC,QAAM,MAAM,EAAE,UAAU,UAAU,WAAW,QAAQ;AACrD,UAAQ,YAAY,UAAU,UAAU,GAAG;AAC3C,SAAO;AACT;AAEA,IAAM,QAAQ,OAAO,WAAW,eAAe,CAAC,OAAO,aAAa,8BAA8B,KAAK,OAAO,UAAU,SAAS;AACjI,IAAM,4BAA4B,QAAQ,yBAAY;AACtD,SAAS,OAAO,aAAa;AAC3B,QAAM,MAAM,OAAO,gBAAgB,aAAa,YAAY,WAAW,IAAI;AAC3E,QAAM,WAAW,CAAC,WAAW,IAAI,UAAU,aAAa,OAAO,OAAO;AACpE,UAAM,CAAC,EAAE,WAAW,QAAI,yBAAW,CAAC,MAAM,IAAI,GAAG,CAAC;AAClD,UAAM,QAAQ,IAAI,SAAS;AAC3B,UAAM,eAAW,qBAAO,KAAK;AAC7B,UAAM,kBAAc,qBAAO,QAAQ;AACnC,UAAM,oBAAgB,qBAAO,UAAU;AACvC,UAAM,iBAAa,qBAAO,KAAK;AAC/B,UAAM,sBAAkB,qBAAO;AAC/B,QAAI,gBAAgB,YAAY,QAAQ;AACtC,sBAAgB,UAAU,SAAS,KAAK;AAAA,IAC1C;AACA,QAAI;AACJ,QAAI,mBAAmB;AACvB,QAAI,SAAS,YAAY,SAAS,YAAY,YAAY,YAAY,cAAc,YAAY,cAAc,WAAW,SAAS;AAChI,sBAAgB,SAAS,KAAK;AAC9B,yBAAmB,CAAC,WAAW,gBAAgB,SAAS,aAAa;AAAA,IACvE;AACA,8BAA0B,MAAM;AAC9B,UAAI,kBAAkB;AACpB,wBAAgB,UAAU;AAAA,MAC5B;AACA,eAAS,UAAU;AACnB,kBAAY,UAAU;AACtB,oBAAc,UAAU;AACxB,iBAAW,UAAU;AAAA,IACvB,CAAC;AACD,UAAM,iCAA6B,qBAAO,KAAK;AAC/C,8BAA0B,MAAM;AAC9B,YAAM,WAAW,MAAM;AACrB,YAAI;AACF,gBAAM,YAAY,IAAI,SAAS;AAC/B,gBAAM,iBAAiB,YAAY,QAAQ,SAAS;AACpD,cAAI,CAAC,cAAc,QAAQ,gBAAgB,SAAS,cAAc,GAAG;AACnE,qBAAS,UAAU;AACnB,4BAAgB,UAAU;AAC1B,wBAAY;AAAA,UACd;AAAA,QACF,SAAS,OAAP;AACA,qBAAW,UAAU;AACrB,sBAAY;AAAA,QACd;AAAA,MACF;AACA,YAAM,cAAc,IAAI,UAAU,QAAQ;AAC1C,UAAI,IAAI,SAAS,MAAM,2BAA2B,SAAS;AACzD,iBAAS;AAAA,MACX;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,UAAM,gBAAgB,mBAAmB,gBAAgB,gBAAgB;AACzE,oCAAc,aAAa;AAC3B,WAAO;AAAA,EACT;AACA,SAAO,OAAO,UAAU,GAAG;AAC3B,WAAS,OAAO,QAAQ,IAAI,WAAW;AACrC,YAAQ,KAAK,oEAAoE;AACjF,UAAM,QAAQ,CAAC,UAAU,GAAG;AAC5B,WAAO;AAAA,MACL,OAAO;AACL,cAAM,OAAO,MAAM,UAAU;AAC7B,eAAO,EAAE,OAAO,MAAM,MAAM,GAAG,KAAK;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;", "names": []}