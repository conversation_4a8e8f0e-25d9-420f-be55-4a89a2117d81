import React, { useEffect, useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { useAnimations, useGLTF } from '@react-three/drei';
import * as THREE from 'three';
import { useChat } from '../hooks/useChat';
import { Asset } from 'expo-asset';
import * as Speech from 'expo-speech';

// Facial expressions mapping from the web version
const facialExpressions = {
  default: {},
  smile: {
    browInnerUp: 0.17,
    eyeSquintLeft: 0.4,
    eyeSquintRight: 0.44,
    noseSneerLeft: 0.1700000727403593,
    noseSneerRight: 0.14000002836874015,
    mouthPressLeft: 0.61,
    mouthPressRight: 0.41000000000000003,
  },
  funnyFace: {
    jawLeft: 0.63,
    mouthPucker: 0.53,
    noseSneerLeft: 1,
    noseSneerRight: 0.39,
    mouthLeft: 1,
    eyeLookUpLeft: 1,
    eyeLookUpRight: 1,
    cheekPuff: 0.9999924982764238,
    mouthDimpleLeft: 0.414743888682652,
    mouthRollLower: 0.32,
    mouthSmileLeft: 0.35499733688813034,
    mouthSmileRight: 0.35499733688813034,
  },
  sad: {
    mouthFrownLeft: 1,
    mouthFrownRight: 1,
    mouthShrugLower: 0.78341,
    browInnerUp: 0.452,
    eyeSquintLeft: 0.72,
    eyeSquintRight: 0.75,
    eyeLookDownLeft: 0.5,
    eyeLookDownRight: 0.5,
    jawForward: 1,
  },
  surprised: {
    eyeWideLeft: 0.5,
    eyeWideRight: 0.5,
    jawOpen: 0.351,
    mouthFunnel: 1,
    browInnerUp: 1,
  },
  angry: {
    browDownLeft: 1,
    browDownRight: 1,
    eyeSquintLeft: 1,
    eyeSquintRight: 1,
    jawForward: 1,
    jawLeft: 1,
    mouthShrugLower: 1,
    noseSneerLeft: 1,
    noseSneerRight: 0.42,
    eyeLookDownLeft: 0.16,
    eyeLookDownRight: 0.16,
    cheekSquintLeft: 1,
    cheekSquintRight: 1,
    mouthClose: 0.23,
    mouthFunnel: 0.63,
    mouthDimpleRight: 1,
  },
};

// Viseme mapping for lip sync
const corresponding = {
  A: "viseme_PP",
  B: "viseme_kk",
  C: "viseme_I",
  D: "viseme_AA",
  E: "viseme_O",
  F: "viseme_U",
  G: "viseme_FF",
  H: "viseme_TH",
  X: "viseme_PP",
};

export function Avatar(props) {
  // Temporarily disable 3D model loading until we have the actual files
  // const { nodes, materials, scene } = useGLTF(
  //   Asset.fromModule(require('../../assets/models/64f1a714fe61576b46f27ca2.glb')).uri
  // );

  const { message, onMessagePlayed } = useChat();

  // Animation states
  const [blink, setBlink] = useState(false);
  const [winkLeft, setWinkLeft] = useState(false);
  const [winkRight, setWinkRight] = useState(false);
  const [facialExpression, setFacialExpression] = useState("default");
  const [isSpeaking, setIsSpeaking] = useState(false);

  // Temporarily disable animation loading until we have the actual files
  // const { animations } = useGLTF(
  //   Asset.fromModule(require('../../assets/models/animations.glb')).uri
  // );

  const group = useRef();
  // const { actions, mixer } = useAnimations(animations, group);
  const [animation, setAnimation] = useState("Idle");

  // Handle message changes
  useEffect(() => {
    console.log("Current message:", message);

    if (!message) {
      setAnimation("Idle");
      return;
    }

    // Set animation
    console.log("Setting animation to:", message.animation || "Talking_0");
    setAnimation(message.animation || "Talking_0");

    // Set facial expression
    console.log("Setting facial expression to:", message.facialExpression || "smile");
    setFacialExpression(message.facialExpression || "smile");

    // Handle text-to-speech
    if (message.text) {
      const speakOptions = {
        language: 'en-US',
        pitch: 1.0,
        rate: 0.9,
        onStart: () => {
          console.log("Speech started");
          setIsSpeaking(true);
        },
        onDone: () => {
          console.log("Speech finished");
          setIsSpeaking(false);
          onMessagePlayed();
        },
        onError: (error) => {
          console.error("Speech error:", error);
          setIsSpeaking(false);
          onMessagePlayed();
        }
      };

      Speech.speak(message.text, speakOptions);
    }
  }, [message]);

  // Handle animation changes - temporarily disabled until we have 3D models
  useEffect(() => {
    console.log("Animation system temporarily disabled - no 3D models loaded");
    console.log("Current animation:", animation);

    // TODO: Re-enable when 3D models are available
    // console.log("Available animations:", Object.keys(actions));
    // console.log("Current animation:", animation);

    // if (!actions[animation]) {
    //   console.error(`Animation "${animation}" not found!`);
    //   // Try to use a default animation if the requested one doesn't exist
    //   const availableAnimations = Object.keys(actions);
    //   if (availableAnimations.length > 0) {
    //     console.log(`Falling back to "${availableAnimations[0]}" animation`);
    //     setAnimation(availableAnimations[0]);
    //     return;
    //   }
    // }

    // try {
    //   actions[animation]
    //     .reset()
    //     .fadeIn(mixer.stats.actions.inUse === 0 ? 0 : 0.5)
    //     .play();
    //   console.log(`Successfully playing animation: ${animation}`);
    // } catch (error) {
    //   console.error(`Error playing animation ${animation}:`, error);
    //   // Try to play a default animation if there's an error
    //   try {
    //     const defaultAnimation = "Idle";
    //     console.log(`Trying to play default animation: ${defaultAnimation}`);
    //     actions[defaultAnimation]
    //       .reset()
    //       .fadeIn(0.5)
    //       .play();
    //   } catch (fallbackError) {
    //     console.error("Error playing fallback animation:", fallbackError);
    //   }
    // }

    // return () => {
    //   try {
    //     actions[animation].fadeOut(0.5);
    //   } catch (error) {
    //     console.error(`Error fading out animation ${animation}:`, error);
    //   }
    // };
  }, [animation]);

  // Function to lerp (linearly interpolate) morph targets - temporarily disabled
  const lerpMorphTarget = (target, value, speed = 0.1) => {
    // TODO: Re-enable when 3D models are available
    // scene.traverse((child) => {
    //   if (child.isSkinnedMesh && child.morphTargetDictionary) {
    //     const index = child.morphTargetDictionary[target];
    //     if (
    //       index === undefined ||
    //       child.morphTargetInfluences[index] === undefined
    //     ) {
    //       return;
    //     }
    //     child.morphTargetInfluences[index] = THREE.MathUtils.lerp(
    //       child.morphTargetInfluences[index],
    //       value,
    //       speed
    //     );
    //   }
    // });
  };

  // Handle facial expressions and blinking in animation frame - temporarily disabled
  useFrame(() => {
    // TODO: Re-enable when 3D models are available
    // Apply facial expressions
    // Object.keys(nodes.EyeLeft.morphTargetDictionary).forEach((key) => {
    //   const mapping = facialExpressions[facialExpression];
    //   if (key === "eyeBlinkLeft" || key === "eyeBlinkRight") {
    //     return; // eyes wink/blink are handled separately
    //   }
    //   if (mapping && mapping[key]) {
    //     lerpMorphTarget(key, mapping[key], 0.1);
    //   } else {
    //     lerpMorphTarget(key, 0, 0.1);
    //   }
    // });

    // Handle blinking
    // lerpMorphTarget("eyeBlinkLeft", blink || winkLeft ? 1 : 0, 0.5);
    // lerpMorphTarget("eyeBlinkRight", blink || winkRight ? 1 : 0, 0.5);

    // Handle lip sync for talking
    // const appliedMorphTargets = [];
    // if (message && isSpeaking) {
    //   // Simple random mouth movement for talking
    //   if (Math.random() > 0.8) {
    //     const randomPhoneme = Object.values(corresponding)[
    //       Math.floor(Math.random() * Object.values(corresponding).length)
    //     ];
    //     appliedMorphTargets.push(randomPhoneme);
    //     lerpMorphTarget(randomPhoneme, 1, 0.1);
    //   }
    // }

    // Reset unused mouth shapes
    // Object.values(corresponding).forEach((value) => {
    //   if (appliedMorphTargets.includes(value)) {
    //     return;
    //   }
    //   lerpMorphTarget(value, 0, 0.1);
    // });
  });

  // Set up blinking
  useEffect(() => {
    let blinkTimeout;
    const nextBlink = () => {
      blinkTimeout = setTimeout(() => {
        setBlink(true);
        setTimeout(() => {
          setBlink(false);
          nextBlink();
        }, 200);
      }, THREE.MathUtils.randInt(1000, 5000));
    };
    nextBlink();
    return () => clearTimeout(blinkTimeout);
  }, []);

  // Render a placeholder until 3D models are available
  return (
    <group {...props} dispose={null} ref={group}>
      {/* Placeholder sphere for the avatar */}
      <mesh position={[0, 1, 0]}>
        <sphereGeometry args={[0.5, 32, 32]} />
        <meshStandardMaterial
          color={isSpeaking ? "#ff6b6b" : "#4ecdc4"}
          emissive={isSpeaking ? "#ff3333" : "#2c5aa0"}
          emissiveIntensity={0.2}
        />
      </mesh>

      {/* Eyes */}
      <mesh position={[-0.15, 1.1, 0.4]}>
        <sphereGeometry args={[0.05, 16, 16]} />
        <meshStandardMaterial color={blink ? "#333" : "#fff"} />
      </mesh>
      <mesh position={[0.15, 1.1, 0.4]}>
        <sphereGeometry args={[0.05, 16, 16]} />
        <meshStandardMaterial color={blink ? "#333" : "#fff"} />
      </mesh>

      {/* Pupils */}
      {!blink && (
        <>
          <mesh position={[-0.15, 1.1, 0.45]}>
            <sphereGeometry args={[0.02, 16, 16]} />
            <meshStandardMaterial color="#000" />
          </mesh>
          <mesh position={[0.15, 1.1, 0.45]}>
            <sphereGeometry args={[0.02, 16, 16]} />
            <meshStandardMaterial color="#000" />
          </mesh>
        </>
      )}

      {/* Mouth indicator */}
      <mesh position={[0, 0.85, 0.4]} scale={isSpeaking ? [1.2, 1.2, 1] : [1, 1, 1]}>
        <sphereGeometry args={[0.08, 16, 16]} />
        <meshStandardMaterial
          color={isSpeaking ? "#ff4757" : "#ff6b6b"}
          emissive={isSpeaking ? "#ff1744" : "#000"}
          emissiveIntensity={isSpeaking ? 0.3 : 0}
        />
      </mesh>

      {/* Body placeholder */}
      <mesh position={[0, 0, 0]}>
        <cylinderGeometry args={[0.3, 0.4, 1.5, 8]} />
        <meshStandardMaterial color="#95a5a6" />
      </mesh>
    </group>
  );
}
