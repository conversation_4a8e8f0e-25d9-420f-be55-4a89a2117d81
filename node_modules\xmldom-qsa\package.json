{"name": "xmldom-qsa", "version": "1.1.3", "description": "Based on @xmldom/xmldom with some minor enhancements. Add querySelector, querySelectorAll and match method to Document and Element.", "keywords": ["w3c", "dom", "xml", "parser", "javascript", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XMLSerializer", "ponyfill", "querySelector"], "author": "Zelig <<EMAIL>> (https://github.com/zeligzhou)", "homepage": "https://github.com/zeligzhou/xmldom-qsa", "repository": {"type": "git", "url": "**************:zeligzhou/xmldom-qsa.git"}, "main": "lib/index.js", "types": "index.d.ts", "files": ["CHANGELOG.md", "LICENSE", "readme.md", "SECURITY.md", "index.d.ts", "lib"], "scripts": {"lint": "eslint lib test", "format": "prettier --write test", "changelog": "auto-changelog --unreleased-only", "start": "nodemon --watch package.json --watch lib --watch test --exec 'npm --silent run test && npm --silent run lint'", "stryker": "stryker run", "stryker:dry-run": "stryker run -m '' --reporters progress", "test": "jest", "testrelease": "npm test && eslint lib"}, "engines": {"node": ">=8.0.0"}, "dependencies": {}, "devDependencies": {"@stryker-mutator/core": "5.6.1", "auto-changelog": "2.4.0", "eslint": "8.25.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-es5": "1.5.0", "eslint-plugin-prettier": "4.2.1", "get-stream": "6.0.1", "jest": "27.5.1", "nodemon": "2.0.20", "np": "7.6.2", "prettier": "2.7.1", "xmltest": "1.5.0", "yauzl": "2.10.0"}, "bugs": {"email": "<EMAIL>", "url": "https://github.com/zeligzhou/xmldom-qsa/issues"}, "license": "MIT"}