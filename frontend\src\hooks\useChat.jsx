import { createContext, useContext, useEffect, useState } from "react";

// Use the current hostname for the backend URL to support mobile access
const getBackendUrl = () => {
  const hostname = window.location.hostname;
  return import.meta.env.VITE_API_URL || `http://${hostname}:3000`;
};

const backendUrl = getBackendUrl();

const ChatContext = createContext();

export const ChatProvider = ({ children }) => {
  const chat = async (message) => {
    console.log("Sending message to backend:", message);
    console.log("Backend URL:", backendUrl);
    setLoading(true);

    // Set a timeout to clear the loading state after 10 seconds
    const loadingTimeout = setTimeout(() => {
      console.log("Loading timeout reached, clearing loading state");
      setLoading(false);

      // Add a fallback message if we're still loading after timeout
      const fallbackResponses = [
        {
          text: "I'm having trouble responding right now. Please try again in a moment.",
          facialExpression: "sad",
          animation: "Talking_0",
        }
      ];
      console.log("Setting timeout fallback messages:", fallbackResponses);
      setMessages((messages) => [...messages, ...fallbackResponses]);
    }, 10000);

    try {
      console.log("Fetching from:", `${backendUrl}/chat`);
      const data = await fetch(`${backendUrl}/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ message }),
      });

      console.log("Response status:", data.status);
      if (!data.ok) {
        throw new Error(`HTTP error! status: ${data.status}`);
      }

      const jsonData = await data.json();
      console.log("Response data:", jsonData);
      const resp = jsonData.messages || [];
      console.log("Extracted messages:", resp);

      // Clear the loading timeout since we got a response
      clearTimeout(loadingTimeout);

      if (resp.length === 0) {
        console.log("No messages returned, using fallback");
        // Fallback responses if the backend doesn't return any messages
        const fallbackResponses = [
          {
            text: "Hello there! How are you doing today?",
            facialExpression: "smile",
            animation: "Talking_1",
          }
        ];
        console.log("Setting fallback messages:", fallbackResponses);
        setMessages((messages) => [...messages, ...fallbackResponses]);
      } else {
        console.log("Setting messages from response:", resp);
        setMessages((messages) => [...messages, ...resp]);
      }
    } catch (error) {
      console.error("Error fetching response:", error);

      // Clear the loading timeout since we got an error
      clearTimeout(loadingTimeout);

      // Fallback responses if there's an error
      const fallbackResponses = [
        {
          text: "I'm having trouble connecting right now. But I'm still here for you!",
          facialExpression: "sad",
          animation: "Talking_0",
        }
      ];
      console.log("Setting error fallback messages:", fallbackResponses);
      setMessages((messages) => [...messages, ...fallbackResponses]);
    } finally {
      // Clear the loading timeout and set loading to false
      clearTimeout(loadingTimeout);
      setLoading(false);
    }
  };
  const [messages, setMessages] = useState([]);
  const [message, setMessage] = useState();
  const [loading, setLoading] = useState(false);
  const [cameraZoomed, setCameraZoomed] = useState(true);
  const [debug, setDebug] = useState(true); // Enable debug mode by default
  const onMessagePlayed = () => {
    console.log("Message played, current messages:", messages);
    console.log("Current message being played:", message);

    if (messages.length === 0) {
      console.log("No messages to remove");
      return;
    }

    setMessages((messages) => {
      console.log("Removing first message, remaining:", messages.slice(1));
      return messages.slice(1);
    });
  };

  useEffect(() => {
    console.log("Messages array:", messages);
    if (messages.length > 0) {
      console.log("Setting current message to:", messages[0]);
      setMessage(messages[0]);
    } else {
      console.log("No messages, setting message to null");
      setMessage(null);
    }
  }, [messages]);

  return (
    <ChatContext.Provider
      value={{
        chat,
        message,
        onMessagePlayed,
        loading,
        cameraZoomed,
        setCameraZoomed,
        debug,
        setDebug,
        messages, // Add messages to the context
      }}
    >
      {children}
      {debug && (
        <div className="fixed bottom-0 right-0 bg-black bg-opacity-70 text-white p-4 m-4 rounded-lg max-w-md max-h-96 overflow-auto z-50">
          <h3 className="font-bold mb-2">Debug Panel</h3>
          <button
            className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded"
            onClick={() => setDebug(false)}
          >
            X
          </button>
          <div>
            <p>Current Message: {message ? message.text : 'None'}</p>
            <p>Animation: {message ? message.animation : 'None'}</p>
            <p>Facial Expression: {message ? message.facialExpression : 'None'}</p>
            <p>Loading: {loading ? 'Yes' : 'No'}</p>
            <p>Messages Queue: {messages.length}</p>

            <div className="mt-2 flex gap-2">
              <button
                className="bg-blue-500 text-white px-3 py-1 rounded"
                onClick={() => chat("hello")}
                disabled={loading}
              >
                Test "Hello"
              </button>
              <button
                className="bg-green-500 text-white px-3 py-1 rounded"
                onClick={() => chat("hi dear how was your day")}
                disabled={loading}
              >
                Test "Hi dear"
              </button>
              <button
                className="bg-yellow-500 text-white px-3 py-1 rounded"
                onClick={() => chat("tell me a joke")}
                disabled={loading}
              >
                Test "Joke"
              </button>
            </div>

            <div className="mt-2">
              <h4 className="font-semibold">Messages:</h4>
              <ul className="list-disc pl-5">
                {messages.map((msg, i) => (
                  <li key={i} className="mb-1">
                    <p>{msg.text}</p>
                    <p className="text-xs text-gray-300">Animation: {msg.animation}</p>
                    <p className="text-xs text-gray-300">Expression: {msg.facialExpression}</p>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </ChatContext.Provider>
  );
};

export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error("useChat must be used within a ChatProvider");
  }
  return context;
};
