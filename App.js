import React, { Suspense, useEffect } from 'react';
import { StyleSheet, View, LogBox } from 'react-native';
import { Canvas } from '@react-three/fiber/native';
import { StatusBar } from 'expo-status-bar';
import { Asset } from 'expo-asset';
import { ChatProvider } from './src/hooks/useChat';
import { Experience } from './src/components/Experience';
import { UI } from './src/components/UI';

// Ignore specific warnings
LogBox.ignoreLogs([
  'Possible Unhandled Promise Rejection',
  'Require cycle',
  'Non-serializable values were found in the navigation state',
]);

// Preload 3D models
const preloadAssets = async () => {
  try {
    // Comment out for now until we have the actual model files
    // await Asset.loadAsync([
    //   require('./assets/models/64f1a714fe61576b46f27ca2.glb'),
    //   require('./assets/models/animations.glb'),
    // ]);
    console.log('Asset preloading skipped - models not available yet');
  } catch (error) {
    console.log('Error preloading assets:', error);
  }
};

export default function App() {
  useEffect(() => {
    preloadAssets();
  }, []);

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <ChatProvider>
        <Canvas
          style={styles.canvas}
          gl={{ physicallyCorrectLights: true }}
          camera={{ position: [0, 0, 1], fov: 30 }}
        >
          <Suspense fallback={null}>
            <Experience />
          </Suspense>
        </Canvas>
        <UI />
      </ChatProvider>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#faaca8',
    backgroundImage: 'linear-gradient(19deg, #faaca8 0%, #ddd6f3 100%)',
  },
  canvas: {
    flex: 1,
  },
});
