/**
 * The following defined constants and descriptions are directly ported from https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Constants
 *
 * Any copyright is dedicated to the Public Domain. http://creativecommons.org/publicdomain/zero/1.0/
 *
 * Contributors
 *
 * See: https://developer.mozilla.org/en-US/profiles/Sheppy
 * See: https://developer.mozilla.org/en-US/profiles/fscholz
 * See: https://developer.mozilla.org/en-US/profiles/AtiX
 * See: https://developer.mozilla.org/en-US/profiles/Sebastianz
 *
 * These constants are defined on the WebGLRenderingContext / WebGL2RenderingContext interface
 */
/**
 * Passed to clear to clear the current depth buffer
 * @constant {number}
 */
export declare const GL_DEPTH_BUFFER_BIT: number;
/**
 * Passed to clear to clear the current stencil buffer
 * @constant {number}
 */
export declare const GL_STENCIL_BUFFER_BIT: number;
/**
 * Passed to clear to clear the current color buffer
 * @constant {number}
 */
export declare const GL_COLOR_BUFFER_BIT: number;
/**
 * Passed to drawElements or drawArrays to draw single points
 * @constant {number}
 */
export declare const GL_POINTS: number;
/**
 * Passed to drawElements or drawArrays to draw lines. Each vertex connects to the one after it
 * @constant {number}
 */
export declare const GL_LINES: number;
/**
 * Passed to drawElements or drawArrays to draw lines. Each set of two vertices is treated as a separate line segment
 * @constant {number}
 */
export declare const GL_LINE_LOOP: number;
/**
 * Passed to drawElements or drawArrays to draw a connected group of line segments from the first vertex to the last
 * @constant {number}
 */
export declare const GL_LINE_STRIP: number;
/**
 * Passed to drawElements or drawArrays to draw triangles. Each set of three vertices creates a separate triangle
 * @constant {number}
 */
export declare const GL_TRIANGLES: number;
/**
 * Passed to drawElements or drawArrays to draw a connected group of triangles
 * @constant {number}
 */
export declare const GL_TRIANGLE_STRIP: number;
/**
 * Passed to drawElements or drawArrays to draw a connected group of triangles. Each vertex connects to the previous and the first vertex in the fan
 * @constant {number}
 */
export declare const GL_TRIANGLE_FAN: number;
/**
 * Passed to blendFunc or blendFuncSeparate to turn off a component
 * @constant {number}
 */
export declare const GL_ZERO: number;
/**
 * Passed to blendFunc or blendFuncSeparate to turn on a component
 * @constant {number}
 */
export declare const GL_ONE: number;
/**
 * Passed to blendFunc or blendFuncSeparate to multiply a component by the source elements color
 * @constant {number}
 */
export declare const GL_SRC_COLOR: number;
/**
 * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the source elements color
 * @constant {number}
 */
export declare const GL_ONE_MINUS_SRC_COLOR: number;
/**
 * Passed to blendFunc or blendFuncSeparate to multiply a component by the source's alpha
 * @constant {number}
 */
export declare const GL_SRC_ALPHA: number;
/**
 * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the source's alpha
 * @constant {number}
 */
export declare const GL_ONE_MINUS_SRC_ALPHA: number;
/**
 * Passed to blendFunc or blendFuncSeparate to multiply a component by the destination's alpha
 * @constant {number}
 */
export declare const GL_DST_ALPHA: number;
/**
 * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the destination's alpha
 * @constant {number}
 */
export declare const GL_ONE_MINUS_DST_ALPHA: number;
/**
 * Passed to blendFunc or blendFuncSeparate to multiply a component by the destination's color
 * @constant {number}
 */
export declare const GL_DST_COLOR: number;
/**
 * Passed to blendFunc or blendFuncSeparate to multiply a component by one minus the destination's color
 * @constant {number}
 */
export declare const GL_ONE_MINUS_DST_COLOR: number;
/**
 * Passed to blendFunc or blendFuncSeparate to multiply a component by the minimum of source's alpha or one minus the destination's alpha
 * @constant {number}
 */
export declare const GL_SRC_ALPHA_SATURATE: number;
/**
 * Passed to blendFunc or blendFuncSeparate to specify a constant color blend function
 * @constant {number}
 */
export declare const GL_CONSTANT_COLOR: number;
/**
 * Passed to blendFunc or blendFuncSeparate to specify one minus a constant color blend function
 * @constant {number}
 */
export declare const GL_ONE_MINUS_CONSTANT_COLOR: number;
/**
 * Passed to blendFunc or blendFuncSeparate to specify a constant alpha blend function
 * @constant {number}
 */
export declare const GL_CONSTANT_ALPHA: number;
/**
 * Passed to blendFunc or blendFuncSeparate to specify one minus a constant alpha blend function
 * @constant {number}
 */
export declare const GL_ONE_MINUS_CONSTANT_ALPHA: number;
/**
 * Passed to blendEquation or blendEquationSeparate to set an addition blend function
 * @constant {number}
 */
export declare const GL_FUNC_ADD: number;
/**
 * Passed to blendEquation or blendEquationSeparate to specify a subtraction blend function (source - destination)
 * @constant {number}
 */
export declare const GL_FUNC_SUBSTRACT: number;
/**
 * Passed to blendEquation or blendEquationSeparate to specify a reverse subtraction blend function (destination - source)
 * @constant {number}
 */
export declare const GL_FUNC_REVERSE_SUBTRACT: number;
/**
 * Passed to getParameter to get the current RGB blend function
 * @constant {number}
 */
export declare const GL_BLEND_EQUATION: number;
/**
 * Passed to getParameter to get the current RGB blend function. Same as BLEND_EQUATION
 * @constant {number}
 */
export declare const GL_BLEND_EQUATION_RGB: number;
/**
 * Passed to getParameter to get the current alpha blend function. Same as BLEND_EQUATION
 * @constant {number}
 */
export declare const GL_BLEND_EQUATION_ALPHA: number;
/**
 * Passed to getParameter to get the current destination RGB blend function
 * @constant {number}
 */
export declare const GL_BLEND_DST_RGB: number;
/**
 * Passed to getParameter to get the current source RGB blend function
 * @constant {number}
 */
export declare const GL_BLEND_SRC_RGB: number;
/**
 * Passed to getParameter to get the current destination alpha blend function
 * @constant {number}
 */
export declare const GL_BLEND_DST_ALPHA: number;
/**
 * Passed to getParameter to get the current source alpha blend function
 * @constant {number}
 */
export declare const GL_BLEND_SRC_ALPHA: number;
/**
 * Passed to getParameter to return a the current blend color
 * @constant {number}
 */
export declare const GL_BLEND_COLOR: number;
/**
 * Passed to getParameter to get the array buffer binding
 * @constant {number}
 */
export declare const GL_ARRAY_BUFFER_BINDING: number;
/**
 * Passed to getParameter to get the current element array buffer
 * @constant {number}
 */
export declare const GL_ELEMENT_ARRAY_BUFFER_BINDING: number;
/**
 * Passed to getParameter to get the current lineWidth (set by the lineWidth method)
 * @constant {number}
 */
export declare const GL_LINE_WIDTH: number;
/**
 * Passed to getParameter to get the current size of a point drawn with gl.POINTS
 * @constant {number}
 */
export declare const GL_ALIASED_POINT_SIZE_RANGE: number;
/**
 * Passed to getParameter to get the range of available widths for a line. Returns a length-2 array with the lo value at 0, and hight at 1
 * @constant {number}
 */
export declare const GL_ALIASED_LINE_WIDTH_RANGE: number;
/**
 * Passed to getParameter to get the current value of cullFace. Should return FRONT, BACK, or FRONT_AND_BACK
 * @constant {number}
 */
export declare const GL_CULL_FACE_MODE: number;
/**
 * Passed to getParameter to determine the current value of frontFace. Should return CW or CCW
 * @constant {number}
 */
export declare const GL_FRONT_FACE: number;
/**
 * Passed to getParameter to return a length-2 array of floats giving the current depth range
 * @constant {number}
 */
export declare const GL_DEPTH_RANGE: number;
/**
 * Passed to getParameter to determine if the depth write mask is enabled
 * @constant {number}
 */
export declare const GL_DEPTH_WRITEMASK: number;
/**
 * Passed to getParameter to determine the current depth clear value
 * @constant {number}
 */
export declare const GL_DEPTH_CLEAR_VALUE: number;
/**
 * Passed to getParameter to get the current depth function. Returns NEVER, ALWAYS, LESS, EQUAL, LEQUAL, GREATER, GEQUAL, or NOTEQUAL
 * @constant {number}
 */
export declare const GL_DEPTH_FUNC: number;
/**
 * Passed to getParameter to get the value the stencil will be cleared to
 * @constant {number}
 */
export declare const GL_STENCIL_CLEAR_VALUE: number;
/**
 * Passed to getParameter to get the current stencil function. Returns NEVER, ALWAYS, LESS, EQUAL, LEQUAL, GREATER, GEQUAL, or NOTEQUAL
 * @constant {number}
 */
export declare const GL_STENCIL_FUNC: number;
/**
 * Passed to getParameter to get the current stencil fail function. Should return KEEP, REPLACE, INCR, DECR, INVERT, INCR_WRAP, or DECR_WRAP
 * @constant {number}
 */
export declare const GL_STENCIL_FAIL: number;
/**
 * Passed to getParameter to get the current stencil fail function should the depth buffer test fail. Should return KEEP, REPLACE, INCR, DECR, INVERT, INCR_WRAP, or DECR_WRAP
 * @constant {number}
 */
export declare const GL_STENCIL_PASS_DEPTH_FAIL: number;
/**
 * Passed to getParameter to get the current stencil fail function should the depth buffer test pass. Should return KEEP, REPLACE, INCR, DECR, INVERT, INCR_WRAP, or DECR_WRAP
 * @constant {number}
 */
export declare const GL_STENCIL_PASS_DEPTH_PASS: number;
/**
 * Passed to getParameter to get the reference value used for stencil tests
 * @constant {number}
 */
export declare const GL_STENCIL_REF: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_VALUE_MASK: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_WRITEMASK: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_BACK_FUNC: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_BACK_FAIL: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_BACK_PASS_DEPTH_FAIL: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_BACK_PASS_DEPTH_PASS: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_BACK_REF: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_BACK_VALUE_MASK: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_BACK_WRITEMASK: number;
/**
 * Returns an Int32Array with four elements for the current viewport dimensions
 * @constant {number}
 */
export declare const GL_VIEWPORT: number;
/**
 * Returns an Int32Array with four elements for the current scissor box dimensions
 * @constant {number}
 */
export declare const GL_SCISSOR_BOX: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_CLEAR_VALUE: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_WRITEMASK: number;
/**
 * @constant {number}
 */
export declare const GL_UNPACK_ALIGNMENT: number;
/**
 * @constant {number}
 */
export declare const GL_PACK_ALIGNMENT: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_TEXTURE_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_VIEWPORT_DIMS: number;
/**
 * @constant {number}
 */
export declare const GL_SUBPIXEL_BITS: number;
/**
 * @constant {number}
 */
export declare const GL_RED_BITS: number;
/**
 * @constant {number}
 */
export declare const GL_GREEN_BITS: number;
/**
 * @constant {number}
 */
export declare const GL_BLUE_BITS: number;
/**
 * @constant {number}
 */
export declare const GL_ALPHA_BITS: number;
/**
 * @constant {number}
 */
export declare const GL_DEPTH_BITS: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_BITS: number;
/**
 * @constant {number}
 */
export declare const GL_POLYGON_OFFSET_UNITS: number;
/**
 * @constant {number}
 */
export declare const GL_POLYGON_OFFSET_FACTOR: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_BINDING_2D: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLE_BUFFERS: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLES: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLE_COVERAGE_VALUE: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLE_COVERAGE_INVERT: number;
/**
 * @constant {number}
 */
export declare const GL_COMPRESSED_TEXTURE_FORMATS: number;
/**
 * @constant {number}
 */
export declare const GL_VENDOR: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERER: number;
/**
 * @constant {number}
 */
export declare const GL_VERSION: number;
/**
 * @constant {number}
 */
export declare const GL_IMPLEMENTATION_COLOR_READ_TYPE: number;
/**
 * @constant {number}
 */
export declare const GL_IMPLEMENTATION_COLOR_READ_FORMAT: number;
/**
 * @constant {number}
 */
export declare const GL_BROWSER_DEFAULT_WEBGL: number;
/**
 * Passed to bufferData as a hint about whether the contents of the buffer are likely to be used often and not change often
 * @constant {number}
 */
export declare const GL_STATIC_DRAW: number;
/**
 * Passed to bufferData as a hint about whether the contents of the buffer are likely to not be used often
 * @constant {number}
 */
export declare const GL_STREAM_DRAW: number;
/**
 * Passed to bufferData as a hint about whether the contents of the buffer are likely to be used often and change often
 * @constant {number}
 */
export declare const GL_DYNAMIC_DRAW: number;
/**
 * Passed to bindBuffer or bufferData to specify the type of buffer being used
 * @constant {number}
 */
export declare const GL_ARRAY_BUFFER: number;
/**
 * Passed to bindBuffer or bufferData to specify the type of buffer being used
 * @constant {number}
 */
export declare const GL_ELEMENT_ARRAY_BUFFER: number;
/**
 * Passed to getBufferParameter to get a buffer's size
 * @constant {number}
 */
export declare const GL_BUFFER_SIZE: number;
/**
 * Passed to getBufferParameter to get the hint for the buffer passed in when it was created
 * @constant {number}
 */
export declare const GL_BUFFER_USAGE: number;
/**
 * Passed to getVertexAttrib to read back the current vertex attribute
 * @constant {number}
 */
export declare const GL_CURRENT_VERTEX_ATTRIB: number;
/**
 * @constant {number}
 */
export declare const GL_VERTEX_ATTRIB_ARRAY_ENABLED: number;
/**
 * @constant {number}
 */
export declare const GL_VERTEX_ATTRIB_ARRAY_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_VERTEX_ATTRIB_ARRAY_STRIDE: number;
/**
 * @constant {number}
 */
export declare const GL_VERTEX_ATTRIB_ARRAY_TYPE: number;
/**
 * @constant {number}
 */
export declare const GL_VERTEX_ATTRIB_ARRAY_NORMALIZED: number;
/**
 * @constant {number}
 */
export declare const GL_VERTEX_ATTRIB_ARRAY_POINTER: number;
/**
 * @constant {number}
 */
export declare const GL_VERTEX_ATTRIB_ARRAY_BUFFER_BINDING: number;
/**
 * Passed to enable/disable to turn on/off culling. Can also be used with getParameter to find the current culling method
 * @constant {number}
 */
export declare const GL_CULL_FACE: number;
/**
 * Passed to cullFace to specify that only front faces should be culled
 * @constant {number}
 */
export declare const GL_FRONT: number;
/**
 * Passed to cullFace to specify that only back faces should be culled
 * @constant {number}
 */
export declare const GL_BACK: number;
/**
 * Passed to cullFace to specify that front and back faces should be culled
 * @constant {number}
 */
export declare const GL_FRONT_AND_BACK: number;
/**
 * Passed to enable/disable to turn on/off blending. Can also be used with getParameter to find the current blending method
 * @constant {number}
 */
export declare const GL_BLEND: number;
/**
 * Passed to enable/disable to turn on/off the depth test. Can also be used with getParameter to query the depth test
 * @constant {number}
 */
export declare const GL_DEPTH_TEST: number;
/**
 * Passed to enable/disable to turn on/off dithering. Can also be used with getParameter to find the current dithering method
 * @constant {number}
 */
export declare const GL_DITHER: number;
/**
 * Passed to enable/disable to turn on/off the polygon offset. Useful for rendering hidden-line images, decals, and or solids with highlighted edges. Can also be used with getParameter to query the scissor test
 * @constant {number}
 */
export declare const GL_POLYGON_OFFSET_FILL: number;
/**
 * Passed to enable/disable to turn on/off the alpha to coverage. Used in multi-sampling alpha channels
 * @constant {number}
 */
export declare const GL_SAMPLE_ALPHA_TO_COVERAGE: number;
/**
 * Passed to enable/disable to turn on/off the sample coverage. Used in multi-sampling
 * @constant {number}
 */
export declare const GL_SAMPLE_COVERAGE: number;
/**
 * Passed to enable/disable to turn on/off the scissor test. Can also be used with getParameter to query the scissor test
 * @constant {number}
 */
export declare const GL_SCISSOR_TEST: number;
/**
 * Passed to enable/disable to turn on/off the stencil test. Can also be used with getParameter to query the stencil test
 * @constant {number}
 */
export declare const GL_STENCIL_TEST: number;
/**
 * Returned from getError
 * @constant {number}
 */
export declare const GL_NO_ERROR: number;
/**
 * Returned from getError
 * @constant {number}
 */
export declare const GL_INVALID_ENUM: number;
/**
 * Returned from getError
 * @constant {number}
 */
export declare const GL_INVALID_VALUE: number;
/**
 * Returned from getError
 * @constant {number}
 */
export declare const GL_INVALID_OPERATION: number;
/**
 * Returned from getError
 * @constant {number}
 */
export declare const GL_OUT_OF_MEMORY: number;
/**
 * Returned from getError
 * @constant {number}
 */
export declare const GL_CONTEXT_LOST_WEBGL: number;
/**
 * Passed to frontFace to specify the front face of a polygon is drawn in the clockwise direction,
 * @constant {number}
 */
export declare const GL_CW: number;
/**
 * Passed to frontFace to specify the front face of a polygon is drawn in the counter clockwise direction
 * @constant {number}
 */
export declare const GL_CCW: number;
/**
 * There is no preference for this behavior
 * @constant {number}
 */
export declare const GL_DONT_CARE: number;
/**
 * The most efficient behavior should be used
 * @constant {number}
 */
export declare const GL_FASTEST: number;
/**
 * The most correct or the highest quality option should be used
 * @constant {number}
 */
export declare const GL_NICEST: number;
/**
 * Hint for the quality of filtering when generating mipmap images with WebGLRenderingContext.generateMipmap()
 * @constant {number}
 */
export declare const GL_GENERATE_MIPMAP_HINT: number;
/**
 * @constant {number}
 */
export declare const GL_BYTE: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_BYTE: number;
/**
 * @constant {number}
 */
export declare const GL_SHORT: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_SHORT: number;
/**
 * @constant {number}
 */
export declare const GL_INT: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT: number;
/**
 * @constant {number}
 */
export declare const GL_DEPTH_COMPONENT: number;
/**
 * @constant {number}
 */
export declare const GL_ALPHA: number;
/**
 * @constant {number}
 */
export declare const GL_RGB: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA: number;
/**
 * @constant {number}
 */
export declare const GL_LUMINANCE: number;
/**
 * @constant {number}
 */
export declare const GL_LUMINANCE_ALPHA: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_SHORT_4_4_4_4: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_SHORT_5_5_5_1: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_SHORT_5_6_5: number;
/**
 * Passed to createShader to define a fragment shader
 * @constant {number}
 */
export declare const GL_FRAGMENT_SHADER: number;
/**
 * Passed to createShader to define a vertex shader
 * @constant {number}
 */
export declare const GL_VERTEX_SHADER: number;
/**
 * Passed to getShaderParamter to get the status of the compilation. Returns false if the shader was not compiled. You can then query getShaderInfoLog to find the exact error
 * @constant {number}
 */
export declare const GL_COMPILE_STATUS: number;
/**
 * Passed to getShaderParamter to determine if a shader was deleted via deleteShader. Returns true if it was, false otherwise
 * @constant {number}
 */
export declare const GL_DELETE_STATUS: number;
/**
 * Passed to getProgramParameter after calling linkProgram to determine if a program was linked correctly. Returns false if there were errors. Use getProgramInfoLog to find the exact error
 * @constant {number}
 */
export declare const GL_LINK_STATUS: number;
/**
 * Passed to getProgramParameter after calling validateProgram to determine if it is valid. Returns false if errors were found
 * @constant {number}
 */
export declare const GL_VALIDATE_STATUS: number;
/**
 * Passed to getProgramParameter after calling attachShader to determine if the shader was attached correctly. Returns false if errors occurred
 * @constant {number}
 */
export declare const GL_ATTACHED_SHADERS: number;
/**
 * Passed to getProgramParameter to get the number of attributes active in a program
 * @constant {number}
 */
export declare const GL_ACTIVE_ATTRIBUTES: number;
/**
 * Passed to getProgramParamter to get the number of uniforms active in a program
 * @constant {number}
 */
export declare const GL_ACTIVE_UNIFORMS: number;
/**
 * The maximum number of entries possible in the vertex attribute list
 * @constant {number}
 */
export declare const GL_MAX_VERTEX_ATTRIBS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_VERTEX_UNIFORM_VECTORS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_VARYING_VECTORS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS: number;
/**
 * Implementation dependent number of maximum texture units. At least 8
 * @constant {number}
 */
export declare const GL_MAX_TEXTURE_IMAGE_UNITS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_FRAGMENT_UNIFORM_VECTORS: number;
/**
 * @constant {number}
 */
export declare const GL_SHADER_TYPE: number;
/**
 * @constant {number}
 */
export declare const GL_SHADING_LANGUAGE_VERSION: number;
/**
 * @constant {number}
 */
export declare const GL_CURRENT_PROGRAM: number;
/**
 * Passed to depthFunction or stencilFunction to specify depth or stencil tests will never pass. i.e. Nothing will be drawn
 * @constant {number}
 */
export declare const GL_NEVER: number;
/**
 * Passed to depthFunction or stencilFunction to specify depth or stencil tests will always pass. i.e. Pixels will be drawn in the order they are drawn
 * @constant {number}
 */
export declare const GL_ALWAYS: number;
/**
 * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is less than the stored value
 * @constant {number}
 */
export declare const GL_LESS: number;
/**
 * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is equals to the stored value
 * @constant {number}
 */
export declare const GL_EQUAL: number;
/**
 *  Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is less than or equal to the stored value
 * @constant {number}
 */
export declare const GL_LEQUAL: number;
/**
 * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is greater than the stored value
 * @constant {number}
 */
export declare const GL_GREATER: number;
/**
 * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is greater than or equal to the stored value
 * @constant {number}
 */
export declare const GL_GEQUAL: number;
/**
 * Passed to depthFunction or stencilFunction to specify depth or stencil tests will pass if the new depth value is not equal to the stored value
 * @constant {number}
 */
export declare const GL_NOTEQUAL: number;
/**
 * @constant {number}
 */
export declare const GL_KEEP: number;
/**
 * @constant {number}
 */
export declare const GL_REPLACE: number;
/**
 * @constant {number}
 */
export declare const GL_INCR: number;
/**
 * @constant {number}
 */
export declare const GL_DECR: number;
/**
 * @constant {number}
 */
export declare const GL_INVERT: number;
/**
 * @constant {number}
 */
export declare const GL_INCR_WRAP: number;
/**
 * @constant {number}
 */
export declare const GL_DECR_WRAP: number;
/**
 * @constant {number}
 */
export declare const GL_NEAREST: number;
/**
 * @constant {number}
 */
export declare const GL_LINEAR: number;
/**
 * @constant {number}
 */
export declare const GL_NEAREST_MIPMAP_NEAREST: number;
/**
 * @constant {number}
 */
export declare const GL_LINEAR_MIPMAP_NEAREST: number;
/**
 * @constant {number}
 */
export declare const GL_NEAREST_MIPMAP_LINEAR: number;
/**
 * @constant {number}
 */
export declare const GL_LINEAR_MIPMAP_LINEAR: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_MAG_FILTER: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_MIN_FILTER: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_WRAP_S: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_WRAP_T: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_2D: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_CUBE_MAP: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_BINDING_CUBE_MAP: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_CUBE_MAP_POSITIVE_X: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_CUBE_MAP_NEGATIVE_X: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_CUBE_MAP_POSITIVE_Y: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_CUBE_MAP_NEGATIVE_Y: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_CUBE_MAP_POSITIVE_Z: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_CUBE_MAP_NEGATIVE_Z: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_CUBE_MAP_TEXTURE_SIZE: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE0: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE1: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE2: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE3: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE4: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE5: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE6: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE7: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE8: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE9: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE10: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE11: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE12: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE13: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE14: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE15: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE16: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE17: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE18: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE19: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE20: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE21: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE22: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE23: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE24: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE25: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE26: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE27: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE28: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE29: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE30: number;
/**
 * A texture unit
 * @constant {number}
 */
export declare const GL_TEXTURE31: number;
/**
 * The current active texture unit
 * @constant {number}
 */
export declare const GL_ACTIVE_TEXTURE: number;
/**
 * @constant {number}
 */
export declare const GL_REPEAT: number;
/**
 * @constant {number}
 */
export declare const GL_CLAMP_TO_EDGE: number;
/**
 * @constant {number}
 */
export declare const GL_MIRRORED_REPEAT: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_VEC2: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_VEC3: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_VEC4: number;
/**
 * @constant {number}
 */
export declare const GL_INT_VEC2: number;
/**
 * @constant {number}
 */
export declare const GL_INT_VEC3: number;
/**
 * @constant {number}
 */
export declare const GL_INT_VEC4: number;
/**
 * @constant {number}
 */
export declare const GL_BOOL: number;
/**
 * @constant {number}
 */
export declare const GL_BOOL_VEC2: number;
/**
 * @constant {number}
 */
export declare const GL_BOOL_VEC3: number;
/**
 * @constant {number}
 */
export declare const GL_BOOL_VEC4: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_MAT2: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_MAT3: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_MAT4: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLER_2D: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLER_CUBE: number;
/**
 * @constant {number}
 */
export declare const GL_LOW_FLOAT: number;
/**
 * @constant {number}
 */
export declare const GL_MEDIUM_FLOAT: number;
/**
 * @constant {number}
 */
export declare const GL_HIGH_FLOAT: number;
/**
 * @constant {number}
 */
export declare const GL_LOW_INT: number;
/**
 * @constant {number}
 */
export declare const GL_MEDIUM_INT: number;
/**
 * @constant {number}
 */
export declare const GL_HIGH_INT: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA4: number;
/**
 * @constant {number}
 */
export declare const GL_RGB5_A1: number;
/**
 * @constant {number}
 */
export declare const GL_RGB565: number;
/**
 * @constant {number}
 */
export declare const GL_DEPTH_COMPONENT16: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_INDEX: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_INDEX8: number;
/**
 * @constant {number}
 */
export declare const GL_DEPTH_STENCIL: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER_WIDTH: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER_HEIGHT: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER_INTERNAL_FORMAT: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER_RED_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER_GREEN_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER_BLUE_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER_ALPHA_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER_DEPTH_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER_STENCIL_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT0: number;
/**
 * @constant {number}
 */
export declare const GL_DEPTH_ATTACHMENT: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL_ATTACHMENT: number;
/**
 * @constant {number}
 */
export declare const GL_DEPTH_STENCIL_ATTACHMENT: number;
/**
 * @constant {number}
 */
export declare const GL_NONE: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_COMPLETE: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_UNSUPPORTED: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_RENDERBUFFER_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_INVALID_FRAMEBUFFER_OPERATION: number;
/**
 * @constant {number}
 */
export declare const GL_UNPACK_FLIP_Y_WEBGL: number;
/**
 * @constant {number}
 */
export declare const GL_UNPACK_PREMULTIPLY_ALPHA_WEBGL: number;
/**
 * @constant {number}
 */
export declare const GL_UNPACK_COLORSPACE_CONVERSION_WEBGL: number;
/**
 * @constant {number}
 */
export declare const GL_READ_BUFFER: number;
/**
 * @constant {number}
 */
export declare const GL_UNPACK_ROW_LENGTH: number;
/**
 * @constant {number}
 */
export declare const GL_UNPACK_SKIP_ROWS: number;
/**
 * @constant {number}
 */
export declare const GL_UNPACK_SKIP_PIXELS: number;
/**
 * @constant {number}
 */
export declare const GL_PACK_ROW_LENGTH: number;
/**
 * @constant {number}
 */
export declare const GL_PACK_SKIP_ROWS: number;
/**
 * @constant {number}
 */
export declare const GL_PACK_SKIP_PIXELS: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_BINDING_3D: number;
/**
 * @constant {number}
 */
export declare const GL_UNPACK_SKIP_IMAGES: number;
/**
 * @constant {number}
 */
export declare const GL_UNPACK_IMAGE_HEIGHT: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_3D_TEXTURE_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_ELEMENTS_VERTICES: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_ELEMENTS_INDICES: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_TEXTURE_LOD_BIAS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_FRAGMENT_UNIFORM_COMPONENTS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_VERTEX_UNIFORM_COMPONENTS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_ARRAY_TEXTURE_LAYERS: number;
/**
 * @constant {number}
 */
export declare const GL_MIN_PROGRAM_TEXEL_OFFSET: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_PROGRAM_TEXEL_OFFSET: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_VARYING_COMPONENTS: number;
/**
 * @constant {number}
 */
export declare const GL_FRAGMENT_SHADER_DERIVATIVE_HINT: number;
/**
 * @constant {number}
 */
export declare const GL_RASTERIZER_DISCARD: number;
/**
 * @constant {number}
 */
export declare const GL_VERTEX_ARRAY_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_VERTEX_OUTPUT_COMPONENTS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_FRAGMENT_INPUT_COMPONENTS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_SERVER_WAIT_TIMEOUT: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_ELEMENT_INDEX: number;
/**
 * @constant {number}
 */
export declare const GL_RED: number;
/**
 * @constant {number}
 */
export declare const GL_RGB8: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA8: number;
/**
 * @constant {number}
 */
export declare const GL_RGB10_A2: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_3D: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_WRAP_R: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_MIN_LOD: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_MAX_LOD: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_BASE_LEVEL: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_MAX_LEVEL: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_COMPARE_MODE: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_COMPARE_FUNC: number;
/**
 * @constant {number}
 */
export declare const GL_SRGB: number;
/**
 * @constant {number}
 */
export declare const GL_SRGB8: number;
/**
 * @constant {number}
 */
export declare const GL_SRGB8_ALPHA8: number;
/**
 * @constant {number}
 */
export declare const GL_COMPARE_REF_TO_TEXTURE: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA32F: number;
/**
 * @constant {number}
 */
export declare const GL_RGB32F: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA16F: number;
/**
 * @constant {number}
 */
export declare const GL_RGB16F: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_2D_ARRAY: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_BINDING_2D_ARRAY: number;
/**
 * @constant {number}
 */
export declare const GL_R11F_G11F_B10F: number;
/**
 * @constant {number}
 */
export declare const GL_RGB9_E5: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA32UI: number;
/**
 * @constant {number}
 */
export declare const GL_RGB32UI: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA16UI: number;
/**
 * @constant {number}
 */
export declare const GL_RGB16UI: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA8UI: number;
/**
 * @constant {number}
 */
export declare const GL_RGB8UI: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA32I: number;
/**
 * @constant {number}
 */
export declare const GL_RGB32I: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA16I: number;
/**
 * @constant {number}
 */
export declare const GL_RGB16I: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA8I: number;
/**
 * @constant {number}
 */
export declare const GL_RGB8I: number;
/**
 * @constant {number}
 */
export declare const GL_RED_INTEGER: number;
/**
 * @constant {number}
 */
export declare const GL_RGB_INTEGER: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA_INTEGER: number;
/**
 * @constant {number}
 */
export declare const GL_R8: number;
/**
 * @constant {number}
 */
export declare const GL_RG8: number;
/**
 * @constant {number}
 */
export declare const GL_R16F: number;
/**
 * @constant {number}
 */
export declare const GL_R32F: number;
/**
 * @constant {number}
 */
export declare const GL_RG16F: number;
/**
 * @constant {number}
 */
export declare const GL_RG32F: number;
/**
 * @constant {number}
 */
export declare const GL_R8I: number;
/**
 * @constant {number}
 */
export declare const GL_R8UI: number;
/**
 * @constant {number}
 */
export declare const GL_R16I: number;
/**
 * @constant {number}
 */
export declare const GL_R16UI: number;
/**
 * @constant {number}
 */
export declare const GL_R32I: number;
/**
 * @constant {number}
 */
export declare const GL_R32UI: number;
/**
 * @constant {number}
 */
export declare const GL_RG8I: number;
/**
 * @constant {number}
 */
export declare const GL_RG8UI: number;
/**
 * @constant {number}
 */
export declare const GL_RG16I: number;
/**
 * @constant {number}
 */
export declare const GL_RG16UI: number;
/**
 * @constant {number}
 */
export declare const GL_RG32I: number;
/**
 * @constant {number}
 */
export declare const GL_RG32UI: number;
/**
 * @constant {number}
 */
export declare const GL_R8_SNORM: number;
/**
 * @constant {number}
 */
export declare const GL_RG8_SNORM: number;
/**
 * @constant {number}
 */
export declare const GL_RGB8_SNORM: number;
/**
 * @constant {number}
 */
export declare const GL_RGBA8_SNORM: number;
/**
 * @constant {number}
 */
export declare const GL_RGB10_A2UI: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_IMMUTABLE_FORMAT: number;
/**
 * @constant {number}
 */
export declare const GL_TEXTURE_IMMUTABLE_LEVELS: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_2_10_10_10_REV: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_10F_11F_11F_REV: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_5_9_9_9_REV: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_32_UNSIGNED_INT_24_8_REV: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_24_8: number;
/**
 * @constant {number}
 */
export declare const GL_HALF_FLOAT: number;
/**
 * @constant {number}
 */
export declare const GL_RG: number;
/**
 * @constant {number}
 */
export declare const GL_RG_INTEGER: number;
/**
 * @constant {number}
 */
export declare const GL_INT_2_10_10_10_REV: number;
/**
 * @constant {number}
 */
export declare const GL_CURRENT_QUERY: number;
/**
 * @constant {number}
 */
export declare const GL_QUERY_RESULT: number;
/**
 * @constant {number}
 */
export declare const GL_QUERY_RESULT_AVAILABLE: number;
/**
 * @constant {number}
 */
export declare const GL_ANY_SAMPLES_PASSED: number;
/**
 * @constant {number}
 */
export declare const GL_ANY_SAMPLES_PASSED_CONSERVATIVE: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_DRAW_BUFFERS: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER0: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER1: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER2: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER3: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER4: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER5: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER6: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER7: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER8: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER9: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER10: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER11: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER12: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER13: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER14: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER15: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_COLOR_ATTACHMENTS: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT1: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT2: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT3: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT4: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT5: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT6: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT7: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT8: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT9: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT10: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT11: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT12: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT13: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT14: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT15: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLER_3D: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLER_2D_SHADOW: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLER_2D_ARRAY: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLER_2D_ARRAY_SHADOW: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLER_CUBE_SHADOW: number;
/**
 * @constant {number}
 */
export declare const GL_INT_SAMPLER_2D: number;
/**
 * @constant {number}
 */
export declare const GL_INT_SAMPLER_3D: number;
/**
 * @constant {number}
 */
export declare const GL_INT_SAMPLER_CUBE: number;
/**
 * @constant {number}
 */
export declare const GL_INT_SAMPLER_2D_ARRAY: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_SAMPLER_2D: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_SAMPLER_3D: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_SAMPLER_CUBE: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_SAMPLER_2D_ARRAY: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_SAMPLES: number;
/**
 * @constant {number}
 */
export declare const GL_SAMPLER_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_PIXEL_PACK_BUFFER: number;
/**
 * @constant {number}
 */
export declare const GL_PIXEL_UNPACK_BUFFER: number;
/**
 * @constant {number}
 */
export declare const GL_PIXEL_PACK_BUFFER_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_PIXEL_UNPACK_BUFFER_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_COPY_READ_BUFFER: number;
/**
 * @constant {number}
 */
export declare const GL_COPY_WRITE_BUFFER: number;
/**
 * @constant {number}
 */
export declare const GL_COPY_READ_BUFFER_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_COPY_WRITE_BUFFER_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_MAT2X3: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_MAT2X4: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_MAT3X2: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_MAT3X4: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_MAT4X2: number;
/**
 * @constant {number}
 */
export declare const GL_FLOAT_MAT4X3: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_VEC2: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_VEC3: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_VEC4: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_NORMALIZED: number;
/**
 * @constant {number}
 */
export declare const GL_SIGNED_NORMALIZED: number;
/**
 * @constant {number}
 */
export declare const GL_VERTEX_ATTRIB_ARRAY_INTEGER: number;
/**
 * @constant {number}
 */
export declare const GL_VERTEX_ATTRIB_ARRAY_DIVISOR: number;
/**
 * @constant {number}
 */
export declare const GL_TRANSFORM_FEEDBACK_BUFFER_MODE: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS: number;
/**
 * @constant {number}
 */
export declare const GL_TRANSFORM_FEEDBACK_VARYINGS: number;
/**
 * @constant {number}
 */
export declare const GL_TRANSFORM_FEEDBACK_BUFFER_START: number;
/**
 * @constant {number}
 */
export declare const GL_TRANSFORM_FEEDBACK_BUFFER_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS: number;
/**
 * @constant {number}
 */
export declare const GL_INTERLEAVED_ATTRIBS: number;
/**
 * @constant {number}
 */
export declare const GL_SEPARATE_ATTRIBS: number;
/**
 * @constant {number}
 */
export declare const GL_TRANSFORM_FEEDBACK_BUFFER: number;
/**
 * @constant {number}
 */
export declare const GL_TRANSFORM_FEEDBACK_BUFFER_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_TRANSFORM_FEEDBACK: number;
/**
 * @constant {number}
 */
export declare const GL_TRANSFORM_FEEDBACK_PAUSED: number;
/**
 * @constant {number}
 */
export declare const GL_TRANSFORM_FEEDBACK_ACTIVE: number;
/**
 * @constant {number}
 */
export declare const GL_TRANSFORM_FEEDBACK_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_RED_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_GREEN_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_BLUE_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_DEFAULT: number;
/**
 * @constant {number}
 */
export declare const GL_DEPTH24_STENCIL8: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_FRAMEBUFFER_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_READ_FRAMEBUFFER: number;
/**
 * @constant {number}
 */
export declare const GL_DRAW_FRAMEBUFFER: number;
/**
 * @constant {number}
 */
export declare const GL_READ_FRAMEBUFFER_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_RENDERBUFFER_SAMPLES: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BUFFER: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BUFFER_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BUFFER_START: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BUFFER_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_VERTEX_UNIFORM_BLOCKS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_FRAGMENT_UNIFORM_BLOCKS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_COMBINED_UNIFORM_BLOCKS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_UNIFORM_BUFFER_BINDINGS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_UNIFORM_BLOCK_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BUFFER_OFFSET_ALIGNMENT: number;
/**
 * @constant {number}
 */
export declare const GL_ACTIVE_UNIFORM_BLOCKS: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_TYPE: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BLOCK_INDEX: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_OFFSET: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_ARRAY_STRIDE: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_MATRIX_STRIDE: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_IS_ROW_MAJOR: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BLOCK_BINDING: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BLOCK_DATA_SIZE: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BLOCK_ACTIVE_UNIFORMS: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER: number;
/**
 * @constant {number}
 */
export declare const GL_UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER: number;
/**
 * @constant {number}
 */
export declare const GL_OBJECT_TYPE: number;
/**
 * @constant {number}
 */
export declare const GL_SYNC_CONDITION: number;
/**
 * @constant {number}
 */
export declare const GL_SYNC_STATUS: number;
/**
 * @constant {number}
 */
export declare const GL_SYNC_FLAGS: number;
/**
 * @constant {number}
 */
export declare const GL_SYNC_FENCE: number;
/**
 * @constant {number}
 */
export declare const GL_SYNC_GPU_COMMANDS_COMPLETE: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNALED: number;
/**
 * @constant {number}
 */
export declare const GL_SIGNALED: number;
/**
 * @constant {number}
 */
export declare const GL_ALREADY_SIGNALED: number;
/**
 * @constant {number}
 */
export declare const GL_TIMEOUT_EXPIRED: number;
/**
 * @constant {number}
 */
export declare const GL_CONDITION_SATISFIED: number;
/**
 * @constant {number}
 */
export declare const GL_WAIT_FAILED: number;
/**
 * @constant {number}
 */
export declare const GL_SYNC_FLUSH_COMMANDS_BIT: number;
/**
 * @constant {number}
 */
export declare const GL_COLOR: number;
/**
 * @constant {number}
 */
export declare const GL_DEPTH: number;
/**
 * @constant {number}
 */
export declare const GL_STENCIL: number;
/**
 * @constant {number}
 */
export declare const GL_MIN: number;
/**
 * @constant {number}
 */
export declare const GL_MAX: number;
/**
 * @constant {number}
 */
export declare const GL_DEPTH_COMPONENT24: number;
/**
 * @constant {number}
 */
export declare const GL_STREAM_READ: number;
/**
 * @constant {number}
 */
export declare const GL_STREAM_COPY: number;
/**
 * @constant {number}
 */
export declare const GL_STATIC_READ: number;
/**
 * @constant {number}
 */
export declare const GL_STATIC_COPY: number;
/**
 * @constant {number}
 */
export declare const GL_DYNAMIC_READ: number;
/**
 * @constant {number}
 */
export declare const GL_DYNAMIC_COPY: number;
/**
 * @constant {number}
 */
export declare const GL_DEPTH_COMPONENT32F: number;
/**
 * @constant {number}
 */
export declare const GL_DEPTH32F_STENCIL8: number;
/**
 * @constant {number}
 */
export declare const GL_INVALID_INDEX: number;
/**
 * @constant {number}
 */
export declare const GL_TIMEOUT_IGNORED: number;
/**
 * @constant {number}
 */
export declare const GL_MAX_CLIENT_WAIT_TIMEOUT_WEBGL: number;
/**
 * Describes the frequency divisor used for instanced rendering
 * @constant {number}
 */
export declare const GL_VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE: number;
/**
 * Passed to getParameter to get the vendor string of the graphics driver
 * @constant {number}
 */
export declare const GL_UNMASKED_VENDOR_WEBGL: number;
/**
 * Passed to getParameter to get the renderer string of the graphics driver
 * @constant {number}
 */
export declare const GL_UNMASKED_RENDERER_WEBGL: number;
/**
 * Returns the maximum available anisotropy
 * @constant {number}
 */
export declare const GL_MAX_TEXTURE_MAX_ANISOTROPY_EXT: number;
/**
 * Passed to texParameter to set the desired maximum anisotropy for a texture
 * @constant {number}
 */
export declare const GL_TEXTURE_MAX_ANISOTROPY_EXT: number;
/**
 * A DXT1-compressed image in an RGB image format
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGB_S3TC_DXT1_EXT: number;
/**
 * A DXT1-compressed image in an RGB image format with a simple on/off alpha value
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_S3TC_DXT1_EXT: number;
/**
 * A DXT3-compressed image in an RGBA image format. Compared to a 32-bit RGBA texture, it offers 4:1 compression
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_S3TC_DXT3_EXT: number;
/**
 * A DXT5-compressed image in an RGBA image format. It also provides a 4:1 compression, but differs to the DXT3 compression in how the alpha compression is done
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_S3TC_DXT5_EXT: number;
/**
 * A DXT1-compressed image in an sRGB image format
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB_S3TC_DXT1_EXT: number;
/**
 * A DXT1-compressed image in an sRGB image format with a simple on/off alpha value
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT: number;
/**
 * A DXT3-compressed image in an sRGBA image format
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT: number;
/**
 * A DXT5-compressed image in an sRGBA image format
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT: number;
/**
 * One-channel (red) unsigned format compression
 * @constant {number}
 */
export declare const GL_COMPRESSED_R11_EAC: number;
/**
 * One-channel (red) signed format compression
 * @constant {number}
 */
export declare const GL_COMPRESSED_SIGNED_R11_EAC: number;
/**
 * Two-channel (red and green) unsigned format compression
 * @constant {number}
 */
export declare const GL_COMPRESSED_RG11_EAC: number;
/**
 * Two-channel (red and green) signed format compression
 * @constant {number}
 */
export declare const GL_COMPRESSED_SIGNED_RG11_EAC: number;
/**
 * Compresses RBG8 data with no alpha channel
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGB8_ETC2: number;
/**
 * Compresses RGBA8 data. The RGB part is encoded the same as RGB_ETC2, but the alpha part is encoded separately
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA8_ETC2_EAC: number;
/**
 * Compresses sRBG8 data with no alpha channel
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ETC2: number;
/**
 * Compresses sRGBA8 data. The sRGB part is encoded the same as SRGB_ETC2, but the alpha part is encoded separately
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC: number;
/**
 * Similar to RGB8_ETC, but with ability to punch through the alpha channel, which means to make it completely opaque or transparent
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2: number;
/**
 * Similar to SRGB8_ETC, but with ability to punch through the alpha channel, which means to make it completely opaque or transparent
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2: number;
/**
 * RGB compression in 4-bit mode. One block for each 4×4 pixels
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGB_PVRTC_4BPPV1_IMG: number;
/**
 * RGBA compression in 4-bit mode. One block for each 4×4 pixels
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_PVRTC_4BPPV1_IMG: number;
/**
 * RGB compression in 2-bit mode. One block for each 8×4 pixels
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGB_PVRTC_2BPPV1_IMG: number;
/**
 * RGBA compression in 2-bit mode. One block for each 8×4 pixels
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_PVRTC_2BPPV1_IMG: number;
/**
 * Compresses 24-bit RGB data with no alpha channel
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGB_ETC1_WEBGL: number;
/**
 * Compresses RGB textures with no alpha channel
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGB_ATC_WEBGL: number;
/**
 * Compresses RGBA textures using explicit alpha encoding (useful when alpha transitions are sharp)
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL: number;
/**
 * Compresses RGBA textures using interpolated alpha encoding (useful when alpha transitions are gradient)
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 4x4
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_4X4_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 5x4
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_5X4_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 5x5
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_5X5_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 6x5
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_6X5_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 6x6
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_6X6_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 8x5
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_8X5_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 8x6
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_8X6_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 8x8
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_8X8_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 10x5
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_10X5_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 10x6
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_10X6_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 10x8
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_10X8_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 10x10
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_10X10_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 12x10
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_12X10_KHR: number;
/**
 * Compresses RGBA textures using ASTC compression in a blocksize of 12x12
 * @constant {number}
 */
export declare const GL_COMPRESSED_RGBA_ASTC_12X12_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 4x4
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 5x4
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 5x5
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 6x5
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 6x6
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 8x5
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 8x6
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 8x8
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x5
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x6
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x8
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 10x10
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 12x10
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR: number;
/**
 * Compresses SRGB8 textures using ASTC compression in a blocksize of 12x12
 * @constant {number}
 */
export declare const GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR: number;
/**
 * Unsigned integer type for 24-bit depth texture data
 * @constant {number}
 */
export declare const GL_UNSIGNED_INT_24_8_WEBGL: number;
/**
 * Half floating-point type (16-bit)
 * @constant {number}
 */
export declare const GL_HALF_FLOAT_OES: number;
/**
 * RGBA 32-bit floating-point color-renderable format
 * @constant {number}
 */
export declare const GL_RGBA32F_EXT: number;
/**
 * RGB 32-bit floating-point color-renderable format
 * @constant {number}
 */
export declare const GL_RGB32F_EXT: number;
/**
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE_EXT: number;
/**
 * @constant {number}
 */
export declare const GL_UNSIGNED_NORMALIZED_EXT: number;
/**
 * Produces the minimum color components of the source and destination colors
 * @constant {number}
 */
export declare const GL_MIN_EXT: number;
/**
 * Produces the maximum color components of the source and destination colors
 * @constant {number}
 */
export declare const GL_MAX_EXT: number;
/**
 * Unsized sRGB format that leaves the precision up to the driver
 * @constant {number}
 */
export declare const GL_SRGB_EXT: number;
/**
 * Unsized sRGB format with unsized alpha component
 * @constant {number}
 */
export declare const GL_SRGB_ALPHA_EXT: number;
/**
 * Sized (8-bit) sRGB and alpha formats
 * @constant {number}
 */
export declare const GL_SRGB8_ALPHA8_EXT: number;
/**
 * Returns the framebuffer color encoding
 * @constant {number}
 */
export declare const GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING_EXT: number;
/**
 * Indicates the accuracy of the derivative calculation for the GLSL built-in functions: dFdx, dFdy, and fwidth
 * @constant {number}
 */
export declare const GL_FRAGMENT_SHADER_DERIVATIVE_HINT_OES: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT0_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT1_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT2_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT3_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT4_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT5_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT6_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT7_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT8_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT9_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT10_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT11_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT12_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT13_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT14_WEBGL: number;
/**
 * Framebuffer color attachment point
 * @constant {number}
 */
export declare const GL_COLOR_ATTACHMENT15_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER0_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER1_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER2_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER3_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER4_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER5_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER6_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER7_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER8_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER9_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER10_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER11_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER12_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER13_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER14_WEBGL: number;
/**
 * Draw buffer
 * @constant {number}
 */
export declare const GL_DRAW_BUFFER15_WEBGL: number;
/**
 * Maximum number of framebuffer color attachment points
 * @constant {number}
 */
export declare const GL_MAX_COLOR_ATTACHMENTS_WEBGL: number;
/**
 * Maximum number of draw buffers
 * @constant {number}
 */
export declare const GL_MAX_DRAW_BUFFERS_WEBGL: number;
/**
 * The bound vertex array object (VAO)
 * @constant {number}
 */
export declare const GL_VERTEX_ARRAY_BINDING_OES: number;
/**
 * The number of bits used to hold the query result for the given target
 * @constant {number}
 */
export declare const GL_QUERY_COUNTER_BITS_EXT: number;
/**
 * The currently active query
 * @constant {number}
 */
export declare const GL_CURRENT_QUERY_EXT: number;
/**
 * The query result
 * @constant {number}
 */
export declare const GL_QUERY_RESULT_EXT: number;
/**
 * A Boolean indicating whether or not a query result is available
 * @constant {number}
 */
export declare const GL_QUERY_RESULT_AVAILABLE_EXT: number;
/**
 * Elapsed time (in nanoseconds)
 * @constant {number}
 */
export declare const GL_TIME_ELAPSED_EXT: number;
/**
 * The current time
 * @constant {number}
 */
export declare const GL_TIMESTAMP_EXT: number;
/**
 * A Boolean indicating whether or not the GPU performed any disjoint operation
 * @constant {number}
 */
export declare const GL_GPU_DISJOINT_EXT: number;
/**
 * Query to determine if the compilation process is complete
 * @constant {number}
 */
export declare const GL_COMPLETION_STATUS_KHR: number;
