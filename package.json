{"name": "mobile-companion", "version": "1.0.0", "main": "App.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "expo": "~53.0.9", "expo-asset": "^11.1.5", "expo-av": "^15.1.4", "expo-speech": "^13.1.7", "expo-status-bar": "~2.2.3", "expo-three": "^8.0.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "^2.25.0", "react-native-reanimated": "^3.17.5", "three": "^0.176.0", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}